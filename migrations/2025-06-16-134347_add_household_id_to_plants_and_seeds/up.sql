-- Add household_id to plants table
ALTER TABLE plants ADD COLUMN household_id INTEGER NOT NULL DEFAULT 1;

-- Add household_id to seeds table
ALTER TABLE seeds ADD COLUMN household_id INTEGER NOT NULL DEFAULT 1;

-- Add foreign key constraints
-- Note: SQLite doesn't support adding foreign key constraints to existing tables
-- So we'll handle this in the application logic for now

-- Update existing plants to belong to the first household
UPDATE plants SET household_id = 1 WHERE household_id IS NULL;

-- Update existing seeds to belong to the first household
UPDATE seeds SET household_id = 1 WHERE household_id IS NULL;
