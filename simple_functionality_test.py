#!/usr/bin/env python3
"""
Simple functionality test for the Garden Planner Web Application
Tests basic server functionality without external dependencies
"""

import subprocess
import time
import urllib.request
import urllib.error
import sys
import os
import signal

def start_server():
    """Start the Rust server in the background"""
    print("Starting Garden Planner server...")
    try:
        # Start the server
        process = subprocess.Popen(
            ["cargo", "run"],
            cwd="/home/<USER>/dev/garden_planner_web",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            preexec_fn=os.setsid
        )
        
        # Wait for server to start
        print("Waiting for server to start...")
        time.sleep(10)
        
        return process
    except Exception as e:
        print(f"Failed to start server: {e}")
        return None

def test_server_response(url, expected_status=200):
    """Test if server responds to a URL"""
    try:
        response = urllib.request.urlopen(url, timeout=10)
        status_code = response.getcode()
        content = response.read().decode('utf-8')

        print(f"✓ {url} - Status: {status_code}")

        # Handle both single expected status and list of acceptable statuses
        if isinstance(expected_status, list):
            if status_code in expected_status:
                return True, content
            else:
                print(f"  Expected status in {expected_status}, got {status_code}")
                return False, content
        else:
            if status_code == expected_status:
                return True, content
            else:
                print(f"  Expected status {expected_status}, got {status_code}")
                return False, content

    except urllib.error.HTTPError as e:
        print(f"✗ {url} - HTTP Error: {e.code}")
        return False, str(e)
    except urllib.error.URLError as e:
        print(f"✗ {url} - URL Error: {e}")
        return False, str(e)
    except Exception as e:
        print(f"✗ {url} - Error: {e}")
        return False, str(e)

def test_basic_functionality():
    """Test basic server functionality"""
    base_url = "http://localhost:8080"
    
    tests = [
        # Basic pages
        (f"{base_url}/", 200, "Homepage"),
        (f"{base_url}/register", 200, "Registration page"),
        (f"{base_url}/login", 200, "Login page"),
        (f"{base_url}/auth/register", 200, "Auth Registration page"),
        (f"{base_url}/auth/login", 200, "Auth Login page"),
        (f"{base_url}/dashboard", [200, 302], "Dashboard (authenticated or redirect)"),

        # Static assets
        (f"{base_url}/static/css/output.css", 200, "CSS file"),

        # API endpoints that should be protected (may return 200 with login form or 302 redirect)
        (f"{base_url}/admin", [200, 302], "Admin (protected)"),
        (f"{base_url}/plants", [200, 302], "Plants (protected)"),
        (f"{base_url}/households", [200, 302], "Households (protected)"),
    ]
    
    passed = 0
    total = len(tests)
    
    print(f"\nRunning {total} basic functionality tests...\n")
    
    for url, expected_status, description in tests:
        print(f"Testing {description}...")
        success, content = test_server_response(url, expected_status)
        if success:
            passed += 1
            # Check for basic HTML structure
            if (expected_status == 200 or (isinstance(expected_status, list) and 200 in expected_status)) and '<html' in content.lower():
                print(f"  ✓ Valid HTML response")
            elif expected_status == 302 or (isinstance(expected_status, list) and 302 in expected_status):
                print(f"  ✓ Proper response (protected route)")
        print()
    
    print(f"Results: {passed}/{total} tests passed")
    return passed == total

def main():
    """Main test function"""
    print("Garden Planner Web Application - Functionality Test")
    print("=" * 50)
    
    # Start the server
    server_process = start_server()
    if not server_process:
        print("Failed to start server. Exiting.")
        return False
    
    try:
        # Run tests
        success = test_basic_functionality()
        
        if success:
            print("\n✓ All basic functionality tests passed!")
            print("The Garden Planner application is working correctly.")
        else:
            print("\n✗ Some tests failed.")
            print("Please check the server logs for more details.")
        
        return success
        
    finally:
        # Clean up: stop the server
        print("\nStopping server...")
        try:
            os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
            server_process.wait(timeout=5)
        except:
            try:
                os.killpg(os.getpgid(server_process.pid), signal.SIGKILL)
            except:
                pass
        print("Server stopped.")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
