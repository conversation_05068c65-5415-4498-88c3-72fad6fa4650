use actix_session::Session;
use actix_web::{web, HttpResponse, Result};
use diesel::prelude::*;
use serde::Deserialize;

use crate::models::user::{User, SafeUser};
use crate::models::plant::Plant;
use crate::models::herba_plant::{HerbaPlant, NewHerbaPlant};
use crate::schema::{users, plants};
use crate::utils::templates::render_template_with_context;
use crate::utils::auth::is_authenticated;
use crate::utils::herba_scraper::scrape_plant_info;
use crate::DbPool;
use bcrypt;

pub async fn admin_dashboard(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if is_admin {
        let mut conn = pool.get().expect("Couldn't get DB connection from pool");

        // Get comprehensive statistics
        let total_users = users::table.count().get_result::<i64>(&mut conn).unwrap_or(0);
        let total_plants = plants::table.count().get_result::<i64>(&mut conn).unwrap_or(0);

        // Get additional statistics
        use crate::schema::{households, properties, seeds, herba_plants, notifications};
        let total_households = households::table.count().get_result::<i64>(&mut conn).unwrap_or(0);
        let total_properties = properties::table.count().get_result::<i64>(&mut conn).unwrap_or(0);
        let total_seeds = seeds::table.count().get_result::<i64>(&mut conn).unwrap_or(0);
        let total_herba_plants = herba_plants::table.count().get_result::<i64>(&mut conn).unwrap_or(0);
        let total_notifications = notifications::table.count().get_result::<i64>(&mut conn).unwrap_or(0);

        let mut ctx = tera::Context::new();
        ctx.insert("total_users", &total_users);
        ctx.insert("total_plants", &total_plants);
        ctx.insert("total_households", &total_households);
        ctx.insert("total_properties", &total_properties);
        ctx.insert("total_seeds", &total_seeds);
        ctx.insert("total_herba_plants", &total_herba_plants);
        ctx.insert("total_notifications", &total_notifications);
        ctx.insert("user_role", &user_role);

        render_template_with_context("admin/dashboard.html", &mut ctx, &session)
    } else {
        Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."))
    }
}

// Add a simple admin index route
pub async fn admin_index(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse, actix_web::Error> {
    admin_dashboard(session, pool).await
}

// User management
pub async fn list_users(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");
    let all_users = users::table
        .load::<User>(&mut conn)
        .expect("Error loading users");

    let safe_users: Vec<SafeUser> = all_users.into_iter().map(SafeUser::from).collect();
    let mut ctx = tera::Context::new();
    ctx.insert("users", &safe_users);
    ctx.insert("user_role", &user_role);

    render_template_with_context("admin/users/list.html", &mut ctx, &session)
}

#[derive(Deserialize)]
pub struct UpdateUserRoleForm {
    pub user_id: i32,
    pub role: String,
}

#[derive(Deserialize)]
pub struct CreateUserForm {
    pub username: String,
    pub password: String,
    pub role: String,
}

#[derive(Deserialize)]
pub struct EditUserForm {
    pub username: String,
    pub role: String,
    pub password: Option<String>, // Optional password update
}

pub async fn update_user_role(
    session: Session,
    form: web::Form<UpdateUserRoleForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_superadmin = user_role == "superadmin";

    if !is_superadmin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: Only superadmin can change user roles."));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    diesel::update(users::table.find(form.user_id))
        .set(users::role.eq(&form.role))
        .execute(&mut conn)
        .expect("Error updating user role");

    Ok(HttpResponse::Found()
        .append_header(("Location", "/admin/users"))
        .finish())
}

// Create new user
pub async fn create_user_form(
    session: Session,
    _pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."));
    }

    let mut ctx = tera::Context::new();
    ctx.insert("user_role", &user_role);

    render_template_with_context("admin/users/create.html", &mut ctx, &session)
}

pub async fn create_user(
    session: Session,
    form: web::Form<CreateUserForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this feature."));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Hash the password
    let hashed_password = bcrypt::hash(&form.password, bcrypt::DEFAULT_COST)
        .map_err(|_| actix_web::error::ErrorInternalServerError("Failed to hash password"))?;

    // Create new user
    use crate::schema::users::dsl::*;
    let new_user = crate::models::user::NewUser {
        username: &form.username,
        password_hash: &hashed_password,
        role: &form.role,
    };

    match diesel::insert_into(users)
        .values(&new_user)
        .execute(&mut conn)
    {
        Ok(_) => Ok(HttpResponse::Found()
            .append_header(("Location", "/admin/users"))
            .finish()),
        Err(e) => {
            eprintln!("Error creating user: {}", e);
            Ok(HttpResponse::InternalServerError()
                .body("Failed to create user"))
        }
    }
}

// Edit user
pub async fn edit_user_form(
    session: Session,
    path: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."));
    }

    let user_id = path.into_inner();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    let user = users::table
        .find(user_id)
        .first::<User>(&mut conn)
        .map_err(|_| actix_web::error::ErrorNotFound("User not found"))?;

    let safe_user = SafeUser::from(user);
    let mut ctx = tera::Context::new();
    ctx.insert("user", &safe_user);
    ctx.insert("user_role", &user_role);

    render_template_with_context("admin/users/edit.html", &mut ctx, &session)
}

pub async fn update_user(
    session: Session,
    path: web::Path<i32>,
    form: web::Form<EditUserForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this feature."));
    }

    let user_id = path.into_inner();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    use crate::schema::users::dsl::*;

    // Update user fields


    let result = if let Some(new_password) = &form.password {
        if !new_password.is_empty() {
            let hashed_password = bcrypt::hash(new_password, bcrypt::DEFAULT_COST)
                .map_err(|_| actix_web::error::ErrorInternalServerError("Failed to hash password"))?;

            diesel::update(users.find(user_id))
                .set((
                    username.eq(&form.username),
                    role.eq(&form.role),
                    password_hash.eq(hashed_password),
                ))
                .execute(&mut conn)
        } else {
            diesel::update(users.find(user_id))
                .set((
                    username.eq(&form.username),
                    role.eq(&form.role),
                ))
                .execute(&mut conn)
        }
    } else {
        diesel::update(users.find(user_id))
            .set((
                username.eq(&form.username),
                role.eq(&form.role),
            ))
            .execute(&mut conn)
    };

    match result {
        Ok(_) => Ok(HttpResponse::Found()
            .append_header(("Location", "/admin/users"))
            .finish()),
        Err(e) => {
            eprintln!("Error updating user: {}", e);
            Ok(HttpResponse::InternalServerError()
                .body("Failed to update user"))
        }
    }
}

// Delete user
pub async fn delete_user(
    session: Session,
    path: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_superadmin = user_role == "superadmin";

    if !is_superadmin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: Only superadmin can delete users."));
    }

    let user_id = path.into_inner();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    use crate::schema::users::dsl::*;
    match diesel::delete(users.find(user_id)).execute(&mut conn) {
        Ok(_) => Ok(HttpResponse::Found()
            .append_header(("Location", "/admin/users"))
            .finish()),
        Err(e) => {
            eprintln!("Error deleting user: {}", e);
            Ok(HttpResponse::InternalServerError()
                .body("Failed to delete user"))
        }
    }
}

// HerbaDB management
pub async fn manage_herba_db(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin" || user_role == "moderator";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Load both regular plants and herba plants
    let all_plants = plants::table
        .load::<Plant>(&mut conn)
        .expect("Error loading plants");

    use crate::schema::herba_plants;
    let herba_plants = herba_plants::table
        .load::<HerbaPlant>(&mut conn)
        .expect("Error loading herba plants");

    let mut ctx = tera::Context::new();
    ctx.insert("plants", &all_plants);
    ctx.insert("herba_plants", &herba_plants);
    ctx.insert("user_role", &user_role);

    render_template_with_context("admin/herba_database.html", &mut ctx, &session)
}

#[derive(Deserialize)]
pub struct ScrapeForm {
    pub plant_name: String,
    pub csrf_token: String,
}

pub async fn scrape_herba_info(
    session: Session,
    form: web::Form<ScrapeForm>,
    _pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin" || user_role == "moderator";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this feature."));
    }

    // Verify CSRF token
    if !crate::utils::csrf::verify_csrf_token(&session, &form.csrf_token) {
        return Ok(HttpResponse::BadRequest().body("Invalid CSRF token"));
    }

    // Scrape plant information
    match scrape_plant_info(&form.plant_name).await {
        Ok(plant_info) => {
            // Serialize plant_info to JSON string for JavaScript
            let plant_info_json = serde_json::to_string(&plant_info)
                .unwrap_or_else(|_| "{}".to_string());

            // Create context for template rendering
            let mut ctx = tera::Context::new();
            ctx.insert("plant_info", &plant_info);
            ctx.insert("plant_info_json", &plant_info_json);
            ctx.insert("user_role", &user_role);
            ctx.insert("plant_name", &form.plant_name);

            render_template_with_context("admin/scraper_result.html", &mut ctx, &session)
        }
        Err(e) => {
            Ok(HttpResponse::InternalServerError()
                .content_type("text/html")
                .body(format!("Error scraping plant information: {}", e)))
        }
    }
}

// Edit HerbaDB entry form
pub async fn edit_herba_form(
    session: Session,
    path: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin" || user_role == "moderator";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."));
    }

    let herba_id = path.into_inner();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    use crate::schema::herba_plants;
    let herba_plant = herba_plants::table
        .find(herba_id)
        .first::<HerbaPlant>(&mut conn)
        .map_err(|_| actix_web::error::ErrorNotFound("HerbaDB entry not found"))?;

    let mut ctx = tera::Context::new();
    ctx.insert("herba_plant", &herba_plant);
    ctx.insert("user_role", &user_role);

    render_template_with_context("admin/herba_edit.html", &mut ctx, &session)
}

// Update HerbaDB entry
#[derive(Deserialize)]
pub struct EditHerbaForm {
    pub latin_name: String,
    pub common_name: String,
    pub family: Option<String>,
    pub genus: Option<String>,
    pub species: Option<String>,
    pub description: Option<String>,
    pub growth_habit: Option<String>,
    pub sun_requirements: Option<String>,
    pub water_requirements: Option<String>,
    pub soil_type: Option<String>,
    pub hardiness_zone: Option<String>,
    pub csrf_token: String,
}

pub async fn update_herba(
    session: Session,
    path: web::Path<i32>,
    form: web::Form<EditHerbaForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin" || user_role == "moderator";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this feature."));
    }

    // Verify CSRF token
    if !crate::utils::csrf::verify_csrf_token(&session, &form.csrf_token) {
        return Ok(HttpResponse::BadRequest().body("Invalid CSRF token"));
    }

    let herba_id = path.into_inner();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    use crate::schema::herba_plants::dsl::*;

    let result = diesel::update(herba_plants.find(herba_id))
        .set((
            latin_name.eq(&form.latin_name),
            common_name.eq(&form.common_name),
            family.eq(&form.family),
            genus.eq(&form.genus),
            species.eq(&form.species),
            description.eq(&form.description),
            growth_habit.eq(&form.growth_habit),
            sun_requirements.eq(&form.sun_requirements),
            water_requirements.eq(&form.water_requirements),
            soil_type.eq(&form.soil_type),
            hardiness_zone.eq(&form.hardiness_zone),
        ))
        .execute(&mut conn);

    match result {
        Ok(_) => Ok(HttpResponse::Found()
            .append_header(("Location", "/admin/herba-db"))
            .finish()),
        Err(e) => {
            eprintln!("Error updating HerbaDB entry: {}", e);
            Ok(HttpResponse::InternalServerError()
                .body("Failed to update HerbaDB entry"))
        }
    }
}

// Delete HerbaDB entry
pub async fn delete_herba(
    session: Session,
    path: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin" || user_role == "moderator";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this feature."));
    }

    let herba_id = path.into_inner();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    use crate::schema::herba_plants::dsl::*;

    let result = diesel::delete(herba_plants.find(herba_id)).execute(&mut conn);

    match result {
        Ok(_) => Ok(HttpResponse::Found()
            .append_header(("Location", "/admin/herba-db"))
            .finish()),
        Err(e) => {
            eprintln!("Error deleting HerbaDB entry: {}", e);
            Ok(HttpResponse::InternalServerError()
                .body("Failed to delete HerbaDB entry"))
        }
    }
}

#[derive(Deserialize)]
pub struct CreateFromScrapeForm {
    pub plant_info: serde_json::Value,
    pub plant_name: String,
}

pub async fn create_herba_from_scrape(
    session: Session,
    form: web::Json<CreateFromScrapeForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin" || user_role == "moderator";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .json(serde_json::json!({"success": false, "error": "Forbidden"})));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Extract plant information from the scraped data
    let plant_info = &form.plant_info;
    let plant_name = &form.plant_name;

    // Pre-process array fields to avoid lifetime issues
    let companion_plants_str = plant_info.get("companion_plants")
        .and_then(|v| v.as_array())
        .map(|arr| arr.iter().filter_map(|v| v.as_str()).collect::<Vec<_>>().join(", "));

    let pest_resistance_str = plant_info.get("pests_diseases")
        .and_then(|v| v.as_array())
        .map(|arr| arr.iter().filter_map(|v| v.as_str()).collect::<Vec<_>>().join(", "));

    let culinary_uses_str = plant_info.get("uses")
        .and_then(|v| v.as_array())
        .map(|arr| arr.iter().filter_map(|v| v.as_str()).collect::<Vec<_>>().join(", "));

    let propagation_methods_str = plant_info.get("propagation_methods")
        .and_then(|v| v.as_array())
        .map(|arr| arr.iter().filter_map(|v| v.as_str()).collect::<Vec<_>>().join(", "));

    // Create NewHerbaPlant from scraped data
    let new_herba = NewHerbaPlant {
        latin_name: plant_info.get("scientific_name")
            .and_then(|v| v.as_str())
            .unwrap_or(plant_name),
        common_name: plant_info.get("name")
            .and_then(|v| v.as_str())
            .unwrap_or(plant_name),
        family: plant_info.get("family").and_then(|v| v.as_str()),
        genus: plant_info.get("genus").and_then(|v| v.as_str()),
        species: plant_info.get("species").and_then(|v| v.as_str()),
        variety: plant_info.get("variety").and_then(|v| v.as_str()),
        description: plant_info.get("description").and_then(|v| v.as_str()),
        growth_habit: plant_info.get("plant_type").and_then(|v| v.as_str()),
        mature_height: plant_info.get("mature_size").and_then(|v| v.as_str()),
        mature_width: plant_info.get("mature_size").and_then(|v| v.as_str()),
        sun_requirements: plant_info.get("light_requirements").and_then(|v| v.as_str()),
        water_requirements: plant_info.get("watering_needs").and_then(|v| v.as_str()),
        soil_type: plant_info.get("soil_type").and_then(|v| v.as_str()),
        soil_ph: plant_info.get("soil_ph").and_then(|v| v.as_str()),
        hardiness_zone: plant_info.get("hardiness_zone").and_then(|v| v.as_str()),
        bloom_time: plant_info.get("bloom_time").and_then(|v| v.as_str()),
        bloom_color: plant_info.get("flower_color").and_then(|v| v.as_str()),
        foliage_color: plant_info.get("foliage_color").and_then(|v| v.as_str()),
        fruit_time: plant_info.get("fruit_time").and_then(|v| v.as_str()),
        companion_plants: companion_plants_str.as_deref(),
        antagonist_plants: None, // Not provided by scraper
        pest_resistance: pest_resistance_str.as_deref(),
        disease_resistance: None, // Not provided by scraper
        edible_parts: plant_info.get("edible_parts").and_then(|v| v.as_str()),
        medicinal_uses: plant_info.get("medicinal_uses").and_then(|v| v.as_str()),
        culinary_uses: culinary_uses_str.as_deref(),
        propagation_methods: propagation_methods_str.as_deref(),
        seed_germination_time: plant_info.get("germination_time")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<i32>().ok()),
        seed_viability: plant_info.get("seed_viability")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<i32>().ok()),
        days_to_maturity: plant_info.get("days_to_maturity")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<i32>().ok()),
        harvest_method: plant_info.get("harvest_method").and_then(|v| v.as_str()),
        storage_method: plant_info.get("storage_method").and_then(|v| v.as_str()),
        nutritional_info: plant_info.get("nutritional_info").and_then(|v| v.as_str()),
    };

    // Save to database
    match HerbaPlant::create(&mut conn, &new_herba) {
        Ok(herba_plant) => {
            Ok(HttpResponse::Ok().json(serde_json::json!({
                "success": true,
                "message": "HerbaDB entry created successfully",
                "herba_plant": herba_plant
            })))
        }
        Err(e) => {
            eprintln!("Error creating HerbaDB entry: {}", e);
            Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "success": false,
                "error": format!("Database error: {}", e)
            })))
        }
    }
}

// Database management routes
pub async fn manage_database(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Get database statistics
    let total_users = users::table.count().get_result::<i64>(&mut conn).unwrap_or(0);
    let total_plants = plants::table.count().get_result::<i64>(&mut conn).unwrap_or(0);

    let mut ctx = tera::Context::new();
    ctx.insert("total_users", &total_users);
    ctx.insert("total_plants", &total_plants);
    ctx.insert("user_role", &user_role);

    render_template_with_context("admin/database.html", &mut ctx, &session)
}

pub async fn manage_settings(
    session: Session,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."));
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Get system statistics for settings page
    let total_users = users::table.count().get_result::<i64>(&mut conn).unwrap_or(0);
    let total_plants = plants::table.count().get_result::<i64>(&mut conn).unwrap_or(0);

    let mut ctx = tera::Context::new();
    ctx.insert("total_users", &total_users);
    ctx.insert("total_plants", &total_plants);
    ctx.insert("user_role", &user_role);

    render_template_with_context("admin/settings.html", &mut ctx, &session)
}

pub async fn manage_notifications(
    session: Session,
    _pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_role = session.get::<String>("role")?.unwrap_or_default();
    let is_admin = user_role == "admin" || user_role == "superadmin";

    if !is_admin {
        return Ok(HttpResponse::Forbidden()
            .body("Forbidden: You do not have access to this page."));
    }

    let mut ctx = tera::Context::new();
    ctx.insert("user_role", &user_role);

    render_template_with_context("admin/notifications.html", &mut ctx, &session)
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/admin")
            .route("", web::get().to(admin_index))
            .route("/dashboard", web::get().to(admin_dashboard))
            .route("/users", web::get().to(list_users))
            .route("/users/create", web::get().to(create_user_form))
            .route("/users/create", web::post().to(create_user))
            .route("/users/{id}/edit", web::get().to(edit_user_form))
            .route("/users/{id}/edit", web::post().to(update_user))
            .route("/users/{id}/delete", web::post().to(delete_user))
            .route("/users/update-role", web::post().to(update_user_role))
            .route("/database", web::get().to(manage_database))
            .route("/settings", web::get().to(manage_settings))
            .route("/notifications", web::get().to(manage_notifications))
            .route("/herba-db", web::get().to(manage_herba_db))
            .route("/herba-db/scrape", web::post().to(scrape_herba_info))
            .route("/herba-db/create-from-scrape", web::post().to(create_herba_from_scrape))
            .route("/herba-db/{id}/edit", web::get().to(edit_herba_form))
            .route("/herba-db/{id}/edit", web::post().to(update_herba))
            .route("/herba-db/{id}/delete", web::post().to(delete_herba)),
    );
}
