use actix_session::Session;
use actix_web::{web, HttpResponse, Result};
use diesel::prelude::*;
use serde::Deserialize;

use crate::models::plot::{NewPlot, Plot};
use crate::utils::templates::render_template_with_context;
use crate::DbPool;
use crate::schema::plots;
use crate::schema::plots::dsl as plots_dsl;

#[derive(Deserialize)]
pub struct PlotForm {
    pub name: String,
    pub description: Option<String>,
    pub parent_id: Option<i32>,
}

pub async fn list_plots(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse, actix_web::Error> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    let all_plots = plots_dsl::plots
        .load::<Plot>(&mut conn)
        .expect("Error loading plots");

    let mut ctx = tera::Context::new();
    ctx.insert("plots", &all_plots);

    render_template_with_context("plots/list.html", &mut ctx, &session)
}

pub async fn new_plot_form(session: Session) -> Result<HttpResponse, actix_web::Error> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut ctx = tera::Context::new();
    render_template_with_context("plots/new.html", &mut ctx, &session)
}

pub async fn create_plot(
    session: Session,
    form: web::Form<PlotForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if session.get::<String>("username")?.is_some() {
        let mut conn = pool.get().expect("Couldn't get DB connection from pool");

        // x and y default to 0 internally
        let new_plot = NewPlot {
            name: &form.name,
            x: 0,
            y: 0,
            description: form.description.as_deref(),
            parent_id: form.parent_id,
        };

        diesel::insert_into(plots_dsl::plots)
            .values(&new_plot)
            .execute(&mut conn)
            .expect("Error inserting new plot");

        Ok(HttpResponse::Found()
            .append_header(("Location", "/plots/list"))
            .finish())
    } else {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish())
    }
}

pub async fn edit_plot_form(
    session: Session,
    plot_id: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if !crate::utils::auth::is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Use *plot_id to get an i32
    let plot = plots_dsl::plots
        .find(*plot_id)
        .first::<Plot>(&mut conn)
        .expect("Error loading plot");

    let mut ctx = tera::Context::new();
    ctx.insert("plot", &plot);

    render_template_with_context("plots/edit.html", &mut ctx, &session)
}

pub async fn update_plot(
    session: Session,
    plot_id: web::Path<i32>,
    form: web::Form<PlotForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if session.get::<String>("username")?.is_some() {
        let mut conn = pool.get().expect("Couldn't get DB connection from pool");

        let existing_plot = plots::table
            .find(*plot_id)
            .first::<Plot>(&mut conn)
            .expect("Error loading existing plot");

        // Keep x and y the same
        let updated_plot = Plot {
            id: *plot_id,
            name: form.name.clone(),
            x: existing_plot.x,
            y: existing_plot.y,
            description: form.description.clone(),
            parent_id: form.parent_id,
        };

        // AsChangeset works if we don't reorder fields. If issues occur, create a separate changeset struct
        diesel::update(plots_dsl::plots.find(*plot_id))
            .set(&updated_plot)
            .execute(&mut conn)
            .expect("Error updating plot");

        Ok(HttpResponse::Found()
            .append_header(("Location", "/plots/list"))
            .finish())
    } else {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish())
    }
}

pub async fn delete_plot(
    session: Session,
    plot_id: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse, actix_web::Error> {
    if session.get::<String>("username")?.is_some() {
        let mut conn = pool.get().expect("Couldn't get DB connection from pool");

        diesel::delete(plots_dsl::plots.find(*plot_id))
            .execute(&mut conn)
            .expect("Error deleting plot");

        Ok(HttpResponse::Found()
            .append_header(("Location", "/plots/list"))
            .finish())
    } else {
        Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish())
    }
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/plots")
            .route("/list", web::get().to(list_plots))
            .route("/new", web::get().to(new_plot_form))
            .route("/create", web::post().to(create_plot))
            .route("/{id}/edit", web::get().to(edit_plot_form))
            .route("/{id}/update", web::post().to(update_plot))
            .route("/{id}/delete", web::post().to(delete_plot)),
    );
}
