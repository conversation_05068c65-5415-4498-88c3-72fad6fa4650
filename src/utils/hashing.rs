//! Password hashing utilities using Argon2
//!
//! This module provides functions for securely hashing and verifying passwords
//! using the Argon2id algorithm, which is recommended for most use cases.

use argon2::{
    password_hash::{PasswordHash, PasswordHasher, PasswordVerifier, SaltString},
    Argon2, Algor<PERSON>m, Version, Params
};
use rand_core::OsRng;
use log::error;
use crate::utils::error::{AppError, AppResult};

/// Hash a password using Argon2id with secure parameters
///
/// # Arguments
/// * `password` - The plaintext password to hash
///
/// # Returns
/// * `AppResult<String>` - The hashed password string or an error
///
/// # Example
/// ```no_run
/// use garden_planner_web::utils::hashing::hash_password;
/// let hashed = hash_password("my_secure_password").unwrap();
/// ```
pub fn hash_password(password: &str) -> AppResult<String> {
    // Generate a random salt
    let salt = SaltString::generate(&mut OsRng);

    // Initialize Argon2id with stronger parameters
    // m_cost: Memory cost (higher is more secure but slower)
    // t_cost: Time cost (iterations - higher is more secure but slower)
    // p_cost: Parallelism factor
    let argon2 = Argon2::new(
        Algorithm::Argon2id, // Recommended algorithm for most use cases
        Version::V0x13,     // Latest version
        Params::new(
            65536,          // 64 MB of memory (m_cost)
            3,              // 3 iterations (t_cost)
            1,              // 1 degree of parallelism (p_cost)
            Some(32)        // Output length (32 bytes)
        ).map_err(|e| {
            error!("Failed to create Argon2 params: {}", e);
            AppError::InternalServerError("Password hashing configuration error".to_string())
        })?
    );

    // Hash the password
    let password_hash = argon2
        .hash_password(password.as_bytes(), &salt)
        .map_err(|e| {
            error!("Failed to hash password: {}", e);
            AppError::InternalServerError("Password hashing failed".to_string())
        })?;

    Ok(password_hash.to_string())
}

/// Verify a password against a stored hash
///
/// # Arguments
/// * `password` - The plaintext password to verify
/// * `hash` - The stored password hash to check against
///
/// # Returns
/// * `bool` - True if the password matches, false otherwise
///
/// # Example
/// ```no_run
/// use garden_planner_web::utils::hashing::verify_password;
/// let stored_hash = "$argon2id$v=19$m=19456,t=2,p=1$...";
/// if verify_password("user_input", stored_hash) {
///     // Password is correct
/// }
/// ```
pub fn verify_password(password: &str, hash: &str) -> bool {
    // Parse the stored password hash
    let parsed_hash = match PasswordHash::new(hash) {
        Ok(hash) => hash,
        Err(e) => {
            error!("Failed to parse password hash: {}", e);
            return false;
        }
    };

    // Initialize Argon2 instance
    let argon2 = Argon2::default();

    // Verify the password against the hash
    argon2
        .verify_password(password.as_bytes(), &parsed_hash)
        .is_ok()
}
