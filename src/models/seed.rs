use diesel::prelude::*;
use serde::{Deserialize, Serialize};

use crate::schema::seeds;

use crate::models::herba_plant::HerbaPlant;

#[derive(Debug, Clone, Queryable, Identifiable, Associations, Serialize, Deserialize, AsChangeset, Selectable)]
#[diesel(table_name = seeds)]
#[diesel(belongs_to(HerbaPlant, foreign_key = herba_id))]
pub struct Seed {
    pub id: i32,
    pub name: String,
    pub note: Option<String>,
    pub origin: Option<String>,
    pub acquisition_year: i32,
    pub expiration_year: i32,
    pub herba_id: i32, // References herba_plants.id
    pub household_id: i32,
    pub source_type: Option<String>,
    pub source_description: Option<String>,
    pub source_plant_id: Option<i32>,
    pub amount: Option<i32>,
    pub unit: Option<String>,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = seeds)]
pub struct NewSeed<'a> {
    pub name: &'a str,
    pub note: Option<&'a str>,
    pub origin: Option<&'a str>,
    pub acquisition_year: i32,
    pub expiration_year: i32,
    pub herba_id: i32,
    pub household_id: i32,
    pub source_type: Option<&'a str>,
    pub source_description: Option<&'a str>,
    pub source_plant_id: Option<i32>,
    pub amount: Option<i32>,
    pub unit: Option<&'a str>,
}