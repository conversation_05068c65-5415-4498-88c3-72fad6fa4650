use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use chrono::NaiveDate;
use crate::schema::plants as plants_table;

#[derive(Debug, Clone, Queryable, Identifiable, Serialize, Deserialize, AsChangeset, Selectable)]
#[diesel(table_name = plants_table)]
pub struct Plant {
    pub id: i32,
    pub name: String,
    pub description: Option<String>,
    pub latin_name: Option<String>,
    pub variety: Option<String>,
    pub note: Option<String>,
    pub nutrient_consumption: Option<String>,
    pub nutrient_deposit: Option<String>,
    pub lighting: Option<String>,
    pub temperature: Option<String>,
    pub light_amount: Option<String>,
    pub sowing_time: Option<String>,
    pub propagation_time: Option<String>,
    pub harvest_time: Option<String>,
    pub growth_duration: Option<String>,
    pub harvest_duration: Option<String>,
    pub field_id: Option<i32>,
    pub herba_plant_id: Option<i32>,
    pub household_id: i32,
    pub last_watering_date: Option<NaiveDate>,
    pub last_fertilizing_date: Option<NaiveDate>,
    pub last_repotting_date: Option<NaiveDate>,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = plants_table)]
pub struct NewPlant<'a> {
    pub name: &'a str,
    pub description: Option<&'a str>,
    pub latin_name: Option<&'a str>,
    pub variety: Option<&'a str>,
    pub note: Option<&'a str>,
    pub nutrient_consumption: Option<&'a str>,
    pub nutrient_deposit: Option<&'a str>,
    pub lighting: Option<&'a str>,
    pub temperature: Option<&'a str>,
    pub light_amount: Option<&'a str>,
    pub sowing_time: Option<&'a str>,
    pub propagation_time: Option<&'a str>,
    pub harvest_time: Option<&'a str>,
    pub growth_duration: Option<&'a str>,
    pub harvest_duration: Option<&'a str>,
    pub field_id: Option<i32>,
    pub herba_plant_id: Option<i32>,
    pub household_id: i32,
    pub last_watering_date: Option<NaiveDate>,
    pub last_fertilizing_date: Option<NaiveDate>,
    pub last_repotting_date: Option<NaiveDate>,
}

impl Plant {
    pub fn find_all(conn: &mut SqliteConnection) -> QueryResult<Vec<Plant>> {
        plants_table::table.load::<Plant>(conn)
    }

    pub fn find_by_id(conn: &mut SqliteConnection, plant_id: i32) -> QueryResult<Option<Plant>> {
        plants_table::table
            .filter(plants_table::id.eq(plant_id))
            .first(conn)
            .optional()
    }

    /// Get the herba plant record for this plant instance
    pub fn get_herba_plant(&self, conn: &mut SqliteConnection) -> QueryResult<Option<crate::models::HerbaPlant>> {
        if let Some(herba_id) = self.herba_plant_id {
            crate::models::HerbaPlant::find_by_id(conn, herba_id)
        } else {
            Ok(None)
        }
    }

    /// Find all plants linked to a specific herba plant
    pub fn find_by_herba_plant_id(conn: &mut SqliteConnection, herba_id: i32) -> QueryResult<Vec<Plant>> {
        plants_table::table
            .filter(plants_table::herba_plant_id.eq(herba_id))
            .load::<Plant>(conn)
    }

    /// Get watering schedule for this plant based on herba data
    pub fn get_watering_schedule(&self, conn: &mut SqliteConnection) -> Option<i32> {
        if let Ok(Some(herba)) = self.get_herba_plant(conn) {
            herba.get_watering_schedule()
        } else {
            Some(5) // Default to every 5 days
        }
    }

    /// Get fertilizing schedule for this plant based on herba data
    pub fn get_fertilizing_schedule(&self, conn: &mut SqliteConnection) -> Option<i32> {
        if let Ok(Some(herba)) = self.get_herba_plant(conn) {
            herba.get_fertilizing_schedule()
        } else {
            Some(21) // Default to every 3 weeks
        }
    }

    /// Check if this plant is compatible with another plant
    pub fn is_compatible_with(&self, conn: &mut SqliteConnection, other_plant: &Plant) -> bool {
        if let (Some(herba_id), Some(other_herba_id)) = (self.herba_plant_id, other_plant.herba_plant_id) {
            if let Ok(Some(herba)) = crate::models::HerbaPlant::find_by_id(conn, herba_id) {
                return herba.is_compatible_with(other_herba_id);
            }
        }
        true // Default to compatible if no herba data
    }

    /// Get companion plants for this plant
    pub fn get_companion_plants(&self, conn: &mut SqliteConnection) -> QueryResult<Vec<Plant>> {
        if let Some(herba_id) = self.herba_plant_id {
            if let Ok(Some(herba)) = crate::models::HerbaPlant::find_by_id(conn, herba_id) {
                let companion_herbas = herba.get_companion_plants(conn)?;
                let mut companion_plants = Vec::new();
                for companion_herba in companion_herbas {
                    let plants = Self::find_by_herba_plant_id(conn, companion_herba.id)?;
                    companion_plants.extend(plants);
                }
                return Ok(companion_plants);
            }
        }
        Ok(vec![])
    }

    /// Get antagonist plants for this plant
    pub fn get_antagonist_plants(&self, conn: &mut SqliteConnection) -> QueryResult<Vec<Plant>> {
        if let Some(herba_id) = self.herba_plant_id {
            if let Ok(Some(herba)) = crate::models::HerbaPlant::find_by_id(conn, herba_id) {
                let antagonist_herbas = herba.get_antagonist_plants(conn)?;
                let mut antagonist_plants = Vec::new();
                for antagonist_herba in antagonist_herbas {
                    let plants = Self::find_by_herba_plant_id(conn, antagonist_herba.id)?;
                    antagonist_plants.extend(plants);
                }
                return Ok(antagonist_plants);
            }
        }
        Ok(vec![])
    }
}
