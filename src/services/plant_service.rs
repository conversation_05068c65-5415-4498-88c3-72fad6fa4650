use diesel::prelude::*;
use diesel::dsl::sql;
use diesel::sql_types::Integer;
use crate::models::plant::{Plant, NewPlant};
use crate::models::HerbaPlant;
use crate::services::herba_gatherer::{<PERSON><PERSON>Gather<PERSON>, PlantIdentifier};
use crate::schema::plants::dsl as plants_dsl;
use crate::DbPool;
use crate::utils::error::{AppError, AppResult};

/// Service for managing plant-related operations
///
/// This service provides methods for creating, reading, updating, and deleting plants
/// in the database. It abstracts away the database operations and provides a clean
/// interface for the routes to use.
pub struct PlantService;

impl PlantService {
    /// Retrieves all plants from the database for a specific household, ordered by name
    ///
    /// # Arguments
    /// * `pool` - The database connection pool
    /// * `household_id` - The household ID to filter plants by
    ///
    /// # Returns
    /// * `AppResult<Vec<Plant>>` - A list of plants for the household on success, or an error
    ///
    /// # Example
    /// ```no_run
    /// use garden_planner_web::services::plant_service::PlantService;
    /// # use garden_planner_web::utils::db::DbPool;
    /// # let pool: DbPool = unimplemented!();
    /// let household_id = 1;
    /// let plants = PlantService::get_plants_for_household(&pool, household_id).unwrap();
    /// ```
    pub fn get_plants_for_household(pool: &DbPool, household_id: i32) -> AppResult<Vec<Plant>> {
        let mut conn = pool.get()?;

        let plants = plants_dsl::plants
            .filter(plants_dsl::household_id.eq(household_id))
            .select(Plant::as_select())
            .order(plants_dsl::name.asc())
            .load::<Plant>(&mut conn)?;

        Ok(plants)
    }

    /// Retrieves all plants from the database (for admin use), ordered by name
    ///
    /// # Arguments
    /// * `pool` - The database connection pool
    ///
    /// # Returns
    /// * `AppResult<Vec<Plant>>` - A list of all plants on success, or an error
    ///
    /// # Example
    /// ```no_run
    /// use garden_planner_web::services::plant_service::PlantService;
    /// # use garden_planner_web::utils::db::DbPool;
    /// # let pool: DbPool = unimplemented!();
    /// let plants = PlantService::get_all_plants(&pool).unwrap();
    /// ```
    pub fn get_all_plants(pool: &DbPool) -> AppResult<Vec<Plant>> {
        let mut conn = pool.get()?;

        let plants = plants_dsl::plants
            .select(Plant::as_select())
            .order(plants_dsl::name.asc())
            .load::<Plant>(&mut conn)?;

        Ok(plants)
    }

    /// Retrieves a single plant by its ID
    ///
    /// # Arguments
    /// * `pool` - The database connection pool
    /// * `id` - The ID of the plant to retrieve
    ///
    /// # Returns
    /// * `AppResult<Option<Plant>>` - The plant if found, None if not found, or an error
    ///
    /// # Example
    /// ```no_run
    /// use garden_planner_web::services::plant_service::PlantService;
    /// # use garden_planner_web::utils::db::DbPool;
    /// # let pool: DbPool = unimplemented!();
    /// let plant = PlantService::get_plant_by_id(&pool, 1).unwrap();
    /// if let Some(plant) = plant {
    ///     println!("Found plant: {}", plant.name);
    /// }
    /// ```
    pub fn get_plant_by_id(pool: &DbPool, id: i32) -> AppResult<Option<Plant>> {
        let mut conn = pool.get()?;

        let plant = plants_dsl::plants
            .select(Plant::as_select())
            .find(id)
            .first::<Plant>(&mut conn)
            .optional()?;

        Ok(plant)
    }

    /// Creates a new plant in the database with automatic herba data gathering
    ///
    /// # Arguments
    /// * `pool` - The database connection pool
    /// * `new_plant` - The plant data to insert
    ///
    /// # Returns
    /// * `AppResult<Plant>` - The created plant with its ID on success, or an error
    ///
    /// # Example
    /// ```no_run
    /// use garden_planner_web::services::plant_service::PlantService;
    /// use garden_planner_web::models::plant::NewPlant;
    /// # use garden_planner_web::utils::db::DbPool;
    /// # let pool: DbPool = unimplemented!();
    /// let new_plant = NewPlant {
    ///     name: "Tomato".to_string(),
    ///     description: Some("A red fruit/vegetable".to_string()),
    ///     household_id: 1,
    ///     herba_plant_id: None,
    ///     planting_date: None,
    ///     harvest_date: None,
    ///     watering_frequency: None,
    ///     fertilizing_frequency: None,
    ///     notes: None,
    /// };
    /// let created_plant = PlantService::create_plant(&pool, &new_plant).unwrap();
    /// ```
    pub fn create_plant(pool: &DbPool, new_plant: &NewPlant) -> AppResult<Plant> {
        let mut conn = pool.get()?;

        diesel::insert_into(plants_dsl::plants)
            .values(new_plant)
            .execute(&mut conn)?;

        // Retrieve the last inserted ID
        let last_id: i32 = diesel::select(sql::<Integer>("last_insert_rowid()"))
            .get_result(&mut conn)?;

        let mut inserted_plant = plants_dsl::plants
            .find(last_id)
            .first::<Plant>(&mut conn)?;

        // Automatically gather herba data if not already set
        if inserted_plant.herba_plant_id.is_none() {
            if let Ok(herba_id) = Self::gather_and_link_herba_data(&mut conn, &inserted_plant) {
                inserted_plant.herba_plant_id = Some(herba_id);
            }
        }

        Ok(inserted_plant)
    }

    /// Gather herba data for a plant and link it
    fn gather_and_link_herba_data(conn: &mut SqliteConnection, plant: &Plant) -> AppResult<i32> {
        let gatherer = HerbaGatherer::new();

        let identifier = PlantIdentifier {
            latin_name: plant.latin_name.clone(),
            common_name: Some(plant.name.clone()),
            partial_name: Some(plant.name.clone()),
            family: None,
            genus: None,
        };

        // This would need to be async in a real implementation
        // For now, we'll create basic herba data
        let herba_data = gatherer.create_basic_plant_data(&identifier);

        let new_herba = crate::models::herba_plant::NewHerbaPlant {
            latin_name: &herba_data.latin_name,
            common_name: &herba_data.common_name,
            family: herba_data.family.as_deref(),
            genus: herba_data.genus.as_deref(),
            species: herba_data.species.as_deref(),
            variety: herba_data.variety.as_deref(),
            description: herba_data.description.as_deref(),
            growth_habit: herba_data.growth_habit.as_deref(),
            mature_height: herba_data.mature_height.as_deref(),
            mature_width: herba_data.mature_width.as_deref(),
            sun_requirements: herba_data.sun_requirements.as_deref(),
            water_requirements: herba_data.water_requirements.as_deref(),
            soil_type: herba_data.soil_type.as_deref(),
            soil_ph: herba_data.soil_ph.as_deref(),
            hardiness_zone: herba_data.hardiness_zone.as_deref(),
            bloom_time: herba_data.bloom_time.as_deref(),
            bloom_color: herba_data.bloom_color.as_deref(),
            foliage_color: herba_data.foliage_color.as_deref(),
            fruit_time: herba_data.fruit_time.as_deref(),
            companion_plants: herba_data.companion_plants.as_deref(),
            antagonist_plants: herba_data.antagonist_plants.as_deref(),
            pest_resistance: herba_data.pest_resistance.as_deref(),
            disease_resistance: herba_data.disease_resistance.as_deref(),
            edible_parts: herba_data.edible_parts.as_deref(),
            medicinal_uses: herba_data.medicinal_uses.as_deref(),
            culinary_uses: herba_data.culinary_uses.as_deref(),
            propagation_methods: herba_data.propagation_methods.as_deref(),
            seed_germination_time: herba_data.seed_germination_time,
            seed_viability: herba_data.seed_viability,
            days_to_maturity: herba_data.days_to_maturity,
            harvest_method: herba_data.harvest_method.as_deref(),
            storage_method: herba_data.storage_method.as_deref(),
            nutritional_info: herba_data.nutritional_info.as_deref(),
        };

        let herba_plant = HerbaPlant::create(conn, &new_herba)?;

        // Update plant with herba_plant_id
        diesel::update(plants_dsl::plants.find(plant.id))
            .set(plants_dsl::herba_plant_id.eq(Some(herba_plant.id)))
            .execute(conn)?;

        Ok(herba_plant.id)
    }

    /// Updates an existing plant in the database
    ///
    /// # Arguments
    /// * `pool` - The database connection pool
    /// * `id` - The ID of the plant to update
    /// * `updated_plant` - The new plant data
    ///
    /// # Returns
    /// * `AppResult<()>` - Success or an error
    /// * Returns NotFoundError if the plant doesn't exist
    ///
    /// # Example
    /// ```no_run
    /// use garden_planner_web::services::plant_service::PlantService;
    /// # use garden_planner_web::utils::db::DbPool;
    /// # let pool: DbPool = unimplemented!();
    /// let mut plant = PlantService::get_plant_by_id(&pool, 1).unwrap().unwrap();
    /// plant.name = "Updated Tomato".to_string();
    /// PlantService::update_plant(&pool, plant.id, &plant).unwrap();
    /// ```
    pub fn update_plant(pool: &DbPool, id: i32, updated_plant: &Plant) -> AppResult<()> {
        let mut conn = pool.get()?;

        let rows_updated = diesel::update(plants_dsl::plants.find(id))
            .set(updated_plant)
            .execute(&mut conn)?;

        if rows_updated == 0 {
            return Err(AppError::NotFoundError(format!("Plant with ID {} not found", id)));
        }

        Ok(())
    }

    /// Deletes a plant from the database
    ///
    /// # Arguments
    /// * `pool` - The database connection pool
    /// * `id` - The ID of the plant to delete
    ///
    /// # Returns
    /// * `AppResult<()>` - Success or an error
    /// * Returns NotFoundError if the plant doesn't exist
    ///
    /// # Example
    /// ```no_run
    /// use garden_planner_web::services::plant_service::PlantService;
    /// # use garden_planner_web::utils::db::DbPool;
    /// # let pool: DbPool = unimplemented!();
    /// PlantService::delete_plant(&pool, 1).unwrap();
    /// ```
    pub fn delete_plant(pool: &DbPool, id: i32) -> AppResult<()> {
        let mut conn = pool.get()?;

        let rows_deleted = diesel::delete(plants_dsl::plants.find(id))
            .execute(&mut conn)?;

        if rows_deleted == 0 {
            return Err(AppError::NotFoundError(format!("Plant with ID {} not found", id)));
        }

        Ok(())
    }
}
