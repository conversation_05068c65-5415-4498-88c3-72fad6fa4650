{% extends "base.html" %}
{% block title %}Home - Garden Planner{% endblock %}
{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Hero Section -->
    <div class="text-center py-16">
        <h1 class="text-display-large font-normal text-surface-900 dark:text-surface-100 mb-6">
            Welcome to Garden Planner{% if user_context.username %}, {{ user_context.username }}{% endif %}!
            <span class="material-icons text-6xl align-middle ml-2">eco</span>
        </h1>
        <p class="text-headline-small text-surface-600 dark:text-surface-300 mb-8 max-w-4xl mx-auto">
            Plan, manage, and optimize your garden with our comprehensive gardening tools.
            Track plants, manage seasons, and get personalized notifications for your garden care.
        </p>

        {% if not user_context.is_authenticated %}
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/auth/register" class="material-button-filled">
                    Get Started
                </a>
                <a href="/auth/login" class="material-button-outlined">
                    Login
                </a>
            </div>
        {% endif %}
    </div>

    {% if user_context.is_authenticated %}
        <!-- Statistics Dashboard -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-12">
            <div class="material-card p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-primary-100 dark:bg-primary-900">
                        <span class="material-icons text-xl text-primary-600 dark:text-primary-300">home</span>
                    </div>
                    <div class="ml-4">
                        <p class="text-label-medium text-surface-600 dark:text-surface-400">My Properties</p>
                        <p class="text-display-small font-normal text-surface-900 dark:text-surface-100">{{ user_properties | default(value=0) }}</p>
                    </div>
                </div>
            </div>
            <div class="material-card p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-secondary-100 dark:bg-secondary-900">
                        <span class="material-icons text-xl text-secondary-600 dark:text-secondary-300">share</span>
                    </div>
                    <div class="ml-4">
                        <p class="text-label-medium text-surface-600 dark:text-surface-400">Shared Properties</p>
                        <p class="text-display-small font-normal text-surface-900 dark:text-surface-100">{{ shared_properties | default(value=0) }}</p>
                    </div>
                </div>
            </div>
            <div class="material-card p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-tertiary-100 dark:bg-tertiary-900">
                        <span class="material-icons text-xl text-tertiary-600 dark:text-tertiary-300">groups</span>
                    </div>
                    <div class="ml-4">
                        <p class="text-label-medium text-surface-600 dark:text-surface-400">Households</p>
                        <p class="text-display-small font-normal text-surface-900 dark:text-surface-100">{{ user_households | default(value=0) }}</p>
                    </div>
                </div>
            </div>
            <div class="material-card p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-primary-100 dark:bg-primary-900">
                        <span class="material-icons text-xl text-primary-600 dark:text-primary-300">local_florist</span>
                    </div>
                    <div class="ml-4">
                        <p class="text-label-medium text-surface-600 dark:text-surface-400">Plants Available</p>
                        <p class="text-display-small font-normal text-surface-900 dark:text-surface-100">{{ total_plants | default(value=0) }}</p>
                    </div>
                </div>
            </div>
            <div class="material-card p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-secondary-100 dark:bg-secondary-900">
                        <span class="material-icons text-xl text-secondary-600 dark:text-secondary-300">inventory</span>
                    </div>
                    <div class="ml-4">
                        <p class="text-label-medium text-surface-600 dark:text-surface-400">Seeds Tracked</p>
                        <p class="text-display-small font-normal text-surface-900 dark:text-surface-100">{{ total_seeds | default(value=0) }}</p>
                    </div>
                </div>
            </div>
            <div class="material-card p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-tertiary-100 dark:bg-tertiary-900">
                        <span class="material-icons text-xl text-tertiary-600 dark:text-tertiary-300">favorite</span>
                    </div>
                    <div class="ml-4">
                        <p class="text-label-medium text-surface-600 dark:text-surface-400">Wishlist Items</p>
                        <p class="text-display-small font-normal text-surface-900 dark:text-surface-100">{{ user_wishlist | default(value=0) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity Section -->
        <div class="material-card-elevated mb-12">
            <div class="px-6 py-4 border-b border-surface-200 dark:border-surface-700">
                <h2 class="text-title-large font-normal text-surface-900 dark:text-surface-100">Recent Activity</h2>
                <p class="text-body-medium text-surface-600 dark:text-surface-400 mt-1">Your latest garden activities and updates</p>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-center p-4 bg-surface-50 dark:bg-surface-800 rounded-lg">
                        <div class="p-2 rounded-full bg-primary-100 dark:bg-primary-900">
                            <span class="material-icons text-lg text-primary-600 dark:text-primary-300">local_florist</span>
                        </div>
                        <div class="ml-4 flex-1">
                            <p class="text-body-medium font-medium text-surface-900 dark:text-surface-100">Plants database updated</p>
                            <p class="text-body-small text-surface-600 dark:text-surface-400">{{ total_plants | default(value=0) }} plants available in your household</p>
                        </div>
                        <span class="text-label-small text-surface-500 dark:text-surface-400">Today</span>
                    </div>
                    <div class="flex items-center p-4 bg-surface-50 dark:bg-surface-800 rounded-lg">
                        <div class="p-2 rounded-full bg-secondary-100 dark:bg-secondary-900">
                            <span class="material-icons text-lg text-secondary-600 dark:text-secondary-300">inventory</span>
                        </div>
                        <div class="ml-4 flex-1">
                            <p class="text-body-medium font-medium text-surface-900 dark:text-surface-100">Seed inventory tracked</p>
                            <p class="text-body-small text-surface-600 dark:text-surface-400">{{ total_seeds | default(value=0) }} seeds in your collection</p>
                        </div>
                        <span class="text-label-small text-surface-500 dark:text-surface-400">Today</span>
                    </div>
                    <div class="flex items-center p-4 bg-surface-50 dark:bg-surface-800 rounded-lg">
                        <div class="p-2 rounded-full bg-tertiary-100 dark:bg-tertiary-900">
                            <span class="material-icons text-lg text-tertiary-600 dark:text-tertiary-300">home</span>
                        </div>
                        <div class="ml-4 flex-1">
                            <p class="text-body-medium font-medium text-surface-900 dark:text-surface-100">Properties configured</p>
                            <p class="text-body-small text-surface-600 dark:text-surface-400">{{ user_properties | default(value=0) }} properties ready for planning</p>
                        </div>
                        <span class="text-label-small text-surface-500 dark:text-surface-400">Today</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions for Authenticated Users -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <div class="material-card p-8 hover:shadow-elevation-3 transition-all duration-200">
                <div class="flex items-center mb-6">
                    <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-xl flex items-center justify-center mr-6">
                        <span class="material-icons text-2xl text-primary-600 dark:text-primary-400">local_florist</span>
                    </div>
                    <h3 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Manage Plants</h3>
                </div>
                <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Add and manage your plant database with detailed information.</p>
                <a href="/plants/list" class="text-primary-700 hover:text-primary-900 dark:text-primary-300 dark:hover:text-primary-100 font-medium text-label-large">View Plants →</a>
            </div>

            <div class="material-card p-8 hover:shadow-elevation-3 transition-all duration-200">
                <div class="flex items-center mb-6">
                    <div class="w-16 h-16 bg-secondary-100 dark:bg-secondary-900 rounded-xl flex items-center justify-center mr-6">
                        <span class="material-icons text-2xl text-secondary-600 dark:text-secondary-400">home</span>
                    </div>
                    <h3 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Properties</h3>
                </div>
                <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Design and manage your garden properties and growing areas.</p>
                <a href="/property" class="text-secondary-700 hover:text-secondary-900 dark:text-secondary-300 dark:hover:text-secondary-100 font-medium text-label-large">View Properties →</a>
            </div>

            <div class="material-card p-8 hover:shadow-elevation-3 transition-all duration-200">
                <div class="flex items-center mb-6">
                    <div class="w-16 h-16 bg-tertiary-100 dark:bg-tertiary-900 rounded-xl flex items-center justify-center mr-6">
                        <span class="material-icons text-2xl text-tertiary-600 dark:text-tertiary-400">event</span>
                    </div>
                    <h3 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Season Plans</h3>
                </div>
                <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Plan your growing seasons and optimize crop rotations.</p>
                <a href="/season_plans" class="text-tertiary-700 hover:text-tertiary-900 dark:text-tertiary-300 dark:hover:text-tertiary-100 font-medium text-label-large">View Plans →</a>
            </div>

            <div class="material-card p-8 hover:shadow-elevation-3 transition-all duration-200">
                <div class="flex items-center mb-6">
                    <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-xl flex items-center justify-center mr-6">
                        <span class="material-icons text-2xl text-primary-600 dark:text-primary-400">inventory</span>
                    </div>
                    <h3 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Seeds & Inventory</h3>
                </div>
                <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Track your seed inventory and planting schedules.</p>
                <a href="/seeds/list" class="text-primary-700 hover:text-primary-900 dark:text-primary-300 dark:hover:text-primary-100 font-medium text-label-large">View Seeds →</a>
            </div>

            <div class="material-card p-8 hover:shadow-elevation-3 transition-all duration-200">
                <div class="flex items-center mb-6">
                    <div class="w-16 h-16 bg-secondary-100 dark:bg-secondary-900 rounded-xl flex items-center justify-center mr-6">
                        <span class="material-icons text-2xl text-secondary-600 dark:text-secondary-400">groups</span>
                    </div>
                    <h3 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Households</h3>
                </div>
                <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Manage and share your garden with household members.</p>
                <a href="/households" class="text-secondary-700 hover:text-secondary-900 dark:text-secondary-300 dark:hover:text-secondary-100 font-medium text-label-large">View Households →</a>
            </div>

            <div class="material-card p-8 hover:shadow-elevation-3 transition-all duration-200">
                <div class="flex items-center mb-6">
                    <div class="w-16 h-16 bg-tertiary-100 dark:bg-tertiary-900 rounded-xl flex items-center justify-center mr-6">
                        <span class="material-icons text-2xl text-tertiary-600 dark:text-tertiary-400">notifications</span>
                    </div>
                    <h3 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Notifications</h3>
                </div>
                <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Stay on top of watering, fertilizing, and care schedules.</p>
                <a href="/notifications/list" class="text-tertiary-700 hover:text-tertiary-900 dark:text-tertiary-300 dark:hover:text-tertiary-100 font-medium text-label-large">View Notifications →</a>
            </div>
        </div>
    {% else %}
        <!-- Features for Non-Authenticated Users -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-12 mb-16">
            <div class="text-center">
                <div class="w-20 h-20 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="material-icons text-3xl text-primary-600 dark:text-primary-400">local_florist</span>
                </div>
                <h3 class="text-headline-small font-normal text-surface-900 dark:text-surface-100 mb-4">Plant Database</h3>
                <p class="text-body-large text-surface-600 dark:text-surface-300">Comprehensive plant information with growing requirements and care instructions.</p>
            </div>

            <div class="text-center">
                <div class="w-20 h-20 bg-secondary-100 dark:bg-secondary-900 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="material-icons text-3xl text-secondary-600 dark:text-secondary-400">event</span>
                </div>
                <h3 class="text-headline-small font-normal text-surface-900 dark:text-surface-100 mb-4">Season Planning</h3>
                <p class="text-body-large text-surface-600 dark:text-surface-300">Plan your garden seasons with automated optimization and crop rotation suggestions.</p>
            </div>

            <div class="text-center">
                <div class="w-20 h-20 bg-tertiary-100 dark:bg-tertiary-900 rounded-full flex items-center justify-center mx-auto mb-6">
                    <span class="material-icons text-3xl text-tertiary-600 dark:text-tertiary-400">notifications</span>
                </div>
                <h3 class="text-headline-small font-normal text-surface-900 dark:text-surface-100 mb-4">Smart Notifications</h3>
                <p class="text-body-large text-surface-600 dark:text-surface-300">Get personalized reminders for watering, fertilizing, and seasonal garden tasks.</p>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
