{% extends "base.html" %}

{% block title %}Notifications - Garden Planner{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-6xl">
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="material-headline">Notifications 🔔</h1>
            <p class="material-body mt-2">Stay updated with your garden care reminders and alerts</p>
        </div>
        <div class="flex items-center space-x-4">
            <button id="generate-care-notifications" class="material-button-outlined">
                <span class="material-icons mr-2">auto_awesome</span>
                Generate Care Notifications
            </button>
            <a href="/notifications/new" class="material-button-filled">
                <span class="material-icons mr-2">add</span>
                Create Notification
            </a>
            {% if user_context.is_admin %}
            <button id="create-broadcast" class="material-button-outlined">
                <span class="material-icons mr-2">campaign</span>
                Broadcast
            </button>
            {% endif %}
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="material-card p-6 text-center">
            <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="material-icons text-primary-600 dark:text-primary-300">schedule</span>
            </div>
            <h3 class="material-title mb-2">Upcoming</h3>
            <p class="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2" id="upcoming-count">0</p>
            <p class="material-caption">Next 7 days</p>
        </div>

        <div class="material-card p-6 text-center">
            <div class="w-12 h-12 bg-amber-100 dark:bg-amber-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="material-icons text-amber-600 dark:text-amber-300">priority_high</span>
            </div>
            <h3 class="material-title mb-2">Overdue</h3>
            <p class="text-3xl font-bold text-amber-600 dark:text-amber-400 mb-2" id="overdue-count">0</p>
            <p class="material-caption">Needs attention</p>
        </div>

        <div class="material-card p-6 text-center">
            <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span class="material-icons text-green-600 dark:text-green-300">check_circle</span>
            </div>
            <h3 class="material-title mb-2">Completed</h3>
            <p class="text-3xl font-bold text-green-600 dark:text-green-400 mb-2" id="completed-count">0</p>
            <p class="material-caption">This week</p>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="material-card-elevated overflow-hidden">
        <div class="px-6 py-4 border-b border-surface-200 dark:border-surface-700">
            <h2 class="material-title">Recent Notifications</h2>
        </div>

        <div class="divide-y divide-surface-200 dark:divide-surface-700">
            {% for notification in notifications %}
            <div class="p-6 hover:bg-surface-50 dark:hover:bg-surface-700 transition-colors">
                <div class="flex items-start justify-between">
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            {% if notification.sent %}
                                <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                    <span class="material-icons text-green-600 dark:text-green-300">check_circle</span>
                                </div>
                            {% else %}
                                <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                                    <span class="material-icons text-primary-600 dark:text-primary-300">schedule</span>
                                </div>
                            {% endif %}
                        </div>
                        <div class="flex-1">
                            <p class="material-body font-medium text-surface-900 dark:text-surface-100">
                                {{ notification.message }}
                            </p>
                            <div class="flex items-center space-x-4 mt-2">
                                <span class="material-caption">
                                    <span class="material-icons text-sm mr-1">schedule</span>
                                    {{ notification.scheduled_time }}
                                </span>
                                {% if notification.plant_id > 0 %}
                                <span class="material-caption">
                                    <span class="material-icons text-sm mr-1">local_florist</span>
                                    Plant ID: {{ notification.plant_id }}
                                </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        {% if notification.sent %}
                            <span class="material-chip-success">Sent</span>
                        {% else %}
                            <span class="material-chip-primary">Pending</span>
                        {% endif %}
                        {% if not notification.sent %}
                        <button onclick="markAsRead({{ notification.id }})" class="material-button-text text-sm">
                            Mark as Read
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if not notifications %}
            <div class="p-12 text-center">
                <span class="material-icons text-6xl text-surface-400 dark:text-surface-600 mb-4 block">notifications_none</span>
                <p class="material-body text-surface-500 dark:text-surface-400 mb-4">No notifications yet.</p>
                <button id="generate-first-notifications" class="material-button-text">
                    Generate your first notifications
                </button>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load notification statistics
    loadNotificationStats();

    // Event listeners
    document.getElementById('generate-care-notifications').addEventListener('click', generateCareNotifications);

    {% if user_context.is_admin %}
    document.getElementById('create-broadcast').addEventListener('click', createBroadcast);
    {% endif %}

    const generateFirstBtn = document.getElementById('generate-first-notifications');
    if (generateFirstBtn) {
        generateFirstBtn.addEventListener('click', generateCareNotifications);
    }

    async function loadNotificationStats() {
        try {
            const response = await fetch('/api/notifications/recent');
            const data = await response.json();

            if (data.notifications) {
                const now = new Date();
                const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

                let upcoming = 0, overdue = 0, completed = 0;

                data.notifications.forEach(notification => {
                    const scheduledTime = new Date(notification.scheduled_time);

                    if (notification.sent) {
                        if (scheduledTime >= weekAgo) {
                            completed++;
                        }
                    } else {
                        if (scheduledTime < now) {
                            overdue++;
                        } else if (scheduledTime <= new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)) {
                            upcoming++;
                        }
                    }
                });

                document.getElementById('upcoming-count').textContent = upcoming;
                document.getElementById('overdue-count').textContent = overdue;
                document.getElementById('completed-count').textContent = completed;
            }
        } catch (error) {
            console.error('Error loading notification stats:', error);
        }
    }

    async function generateCareNotifications() {
        try {
            const response = await fetch('/notifications/generate_care', {
                method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                throw new Error(data.error || 'Failed to generate notifications');
            }
        } catch (error) {
            console.error('Error generating notifications:', error);
            alert('Failed to generate notifications: ' + error.message);
        }
    }

    {% if user_context.is_admin %}
    async function createBroadcast() {
        window.showPrompt('Enter broadcast message:', '', async function(message) {
            if (!message.trim()) return;

            try {
                const formData = new FormData();
                formData.append('message', message);

                const response = await fetch('/notifications/broadcast', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();

                if (data.success) {
                    window.showSuccess(data.message);
                    setTimeout(() => location.reload(), 1500);
                } else {
                    throw new Error(data.error || 'Failed to create broadcast');
                }
            } catch (error) {
                console.error('Error creating broadcast:', error);
                window.showError('Failed to create broadcast: ' + error.message);
            }
        });
    }
    {% endif %}

    window.markAsRead = async function(notificationId) {
        try {
            const response = await fetch(`/api/notifications/${notificationId}/read`, {
                method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
                location.reload();
            } else {
                throw new Error(data.error || 'Failed to mark as read');
            }
        } catch (error) {
            console.error('Error marking notification as read:', error);
            alert('Failed to mark notification as read: ' + error.message);
        }
    };
});
</script>
{% endblock %}