{% extends "base.html" %}
{% block title %}Gardening Area Planner{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
  <div class="material-card p-6">
    <h2 class="text-display-small font-normal text-surface-900 dark:text-surface-100 mb-6">Gardening Area Planner</h2>

    <form hx-post="/gardening_area/create" hx-target="#gardening-area-message" hx-swap="outerHTML" class="mb-8">
      <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label for="plot-type" class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-2">Select Plot Type</label>
          <select id="plot-type" name="plot_type" required
                  class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 dark:bg-sage-700 dark:text-sage-100">
            <option value="garden_bed">Garden Bed</option>
            <option value="field">Field</option>
            <option value="windowsill">Windowsill</option>
            <option value="flowerpot">Flowerpot</option>
            <option value="planter">Planter</option>
          </select>
        </div>
        <div>
          <label for="plot-name" class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-2">Plot Name</label>
          <input type="text" id="plot-name" name="plot_name" required
                 class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 dark:bg-sage-700 dark:text-sage-100">
        </div>
      </div>
      <button type="submit" class="bg-sage-600 hover:bg-sage-700 text-white font-medium px-4 py-2 rounded-md transition-colors">
        Create Plot
      </button>
    </form>
    <div id="gardening-area-message"></div>

    <h3 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Defined Gardening Areas</h3>
    <div id="gardening-areas" class="mb-8">
      <!-- Gardening areas will be dynamically loaded here -->
      <div class="bg-sage-50 dark:bg-sage-700 rounded-md p-4">
        <ul class="space-y-2">
          <li class="flex justify-between items-center p-3 bg-white dark:bg-sage-600 rounded">
            <span class="text-sage-900 dark:text-sage-100">Plot 1 - Garden Bed</span>
            <div class="space-x-2">
              <button hx-post="/gardening_area/edit" data-plot-id="1"
                      class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors">
                Edit
              </button>
              <button hx-post="/gardening_area/delete" data-plot-id="1"
                      class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors">
                Delete
              </button>
            </div>
          </li>
          <li class="flex justify-between items-center p-3 bg-white dark:bg-sage-600 rounded">
            <span class="text-sage-900 dark:text-sage-100">Plot 2 - Flowerpot</span>
            <div class="space-x-2">
              <button hx-post="/gardening_area/edit" data-plot-id="2"
                      class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors">
                Edit
              </button>
              <button hx-post="/gardening_area/delete" data-plot-id="2"
                      class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors">
                Delete
              </button>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <h3 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Interactive Wizard</h3>
    <div class="bg-sage-50 dark:bg-sage-700 rounded-md p-4">
      <p class="text-sage-700 dark:text-sage-300 mb-4">Use the wizard below to design your gardening area step-by-step.</p>
      <button id="start-wizard" class="bg-sage-600 hover:bg-sage-700 text-white font-medium px-4 py-2 rounded-md transition-colors">
        Start Wizard
      </button>
      <div id="wizard-container" class="mt-4">
        <!-- Wizard content will be dynamically loaded here -->
      </div>
    </div>
  </div>
</div>
{% endblock %}