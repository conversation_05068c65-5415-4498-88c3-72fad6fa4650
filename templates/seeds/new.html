{% extends "base.html" %}
{% block title %}Add New Seed - Garden Planner{% endblock %}
{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="material-card-elevated p-8">
        <div class="text-center mb-8">
            <span class="material-icons text-5xl text-primary-600 dark:text-primary-400 mb-4 block">inventory</span>
            <h1 class="text-headline-medium font-normal text-surface-900 dark:text-surface-100 mb-2">Add New Seed</h1>
            <p class="text-body-large text-surface-600 dark:text-surface-400">Add a new seed to your collection</p>
        </div>

        <form method="post" action="/seeds/create" class="space-y-6" onsubmit="return handleFormSubmit(event)">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

            <div>
                <label for="name" class="material-label">Seed Name</label>
                <input type="text"
                       id="name"
                       name="name"
                       required
                       class="material-input"
                       placeholder="e.g., Tomato Cherry Red"
                       onkeypress="handleEnterKey(event)">
                <div class="material-helper-text">Enter the name or variety of the seed</div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="acquisition_year" class="material-label">Acquisition Year</label>
                    <input type="number"
                           id="acquisition_year"
                           name="acquisition_year"
                           required
                           class="material-input"
                           min="1900"
                           max="2030"
                           value="{{ current_year }}"
                           onkeypress="handleEnterKey(event)">
                    <div class="material-helper-text">Year you acquired these seeds</div>
                </div>

                <div>
                    <label for="expiration_year" class="material-label">Expiration Year</label>
                    <input type="number"
                           id="expiration_year"
                           name="expiration_year"
                           required
                           class="material-input"
                           min="2024"
                           max="2040"
                           value="{{ current_year + 3 }}"
                           onkeypress="handleEnterKey(event)">
                    <div class="material-helper-text">Year when seeds expire</div>
                </div>
            </div>

            <div>
                <label for="herba_id" class="material-label">HerbaDB Entry</label>
                <select id="herba_id" name="herba_id" required class="material-select">
                    <option value="">Select a HerbaDB entry...</option>
                    {% for herba_plant in herba_plants %}
                    <option value="{{ herba_plant.id }}">{{ herba_plant.common_name }}{% if herba_plant.latin_name %} ({{ herba_plant.latin_name }}){% endif %}</option>
                    {% endfor %}
                </select>
                <div class="material-helper-text">Choose the HerbaDB entry for this seed type</div>
            </div>

            <!-- Source Selection -->
            <div>
                <label class="material-label">Source</label>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <input type="radio" id="source_text" name="source_type" value="text" checked
                               class="w-4 h-4 text-primary-600 bg-surface-100 border-outline focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-surface-800 focus:ring-2 dark:bg-surface-700 dark:border-surface-600">
                        <label for="source_text" class="ml-2 text-body-medium text-surface-900 dark:text-surface-100">Text description</label>
                    </div>
                    <div class="flex items-center">
                        <input type="radio" id="source_plant" name="source_type" value="plant"
                               class="w-4 h-4 text-primary-600 bg-surface-100 border-outline focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-surface-800 focus:ring-2 dark:bg-surface-700 dark:border-surface-600">
                        <label for="source_plant" class="ml-2 text-body-medium text-surface-900 dark:text-surface-100">From existing plant</label>
                    </div>
                </div>

                <div id="source_text_input" class="mt-3">
                    <input type="text" id="source_description" name="source_description"
                           class="material-input" placeholder="e.g., Local garden center, Online store, Friend's garden">
                </div>

                <div id="source_plant_input" class="mt-3 hidden">
                    <select id="source_plant_id" name="source_plant_id" class="material-select">
                        <option value="">Select source plant...</option>
                        <!-- Will be populated with household plants -->
                    </select>
                </div>
                <div class="material-helper-text">Where did you get these seeds?</div>
            </div>

            <!-- Amount/Quantity -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="amount" class="material-label">Amount</label>
                    <input type="number" id="amount" name="amount" min="1" step="1"
                           class="material-input" placeholder="e.g., 20">
                    <div class="material-helper-text">Number of seeds</div>
                </div>
                <div>
                    <label for="unit" class="material-label">Unit</label>
                    <select id="unit" name="unit" class="material-select">
                        <option value="seeds">Seeds</option>
                        <option value="packets">Packets</option>
                        <option value="grams">Grams</option>
                        <option value="ounces">Ounces</option>
                    </select>
                    <div class="material-helper-text">Unit of measurement</div>
                </div>
            </div>



            <div>
                <label for="origin" class="material-label">Origin (Optional)</label>
                <input type="text"
                       id="origin"
                       name="origin"
                       class="material-input"
                       placeholder="e.g., Local garden center, Online store"
                       onkeypress="handleEnterKey(event)">
                <div class="material-helper-text">Where did you get these seeds?</div>
            </div>

            <div>
                <label for="note" class="material-label">Notes (Optional)</label>
                <textarea id="note"
                          name="note"
                          class="material-textarea"
                          rows="3"
                          placeholder="Any additional notes about these seeds..."></textarea>
                <div class="material-helper-text">Storage conditions, special instructions, etc.</div>
            </div>

            <div class="flex justify-between items-center pt-6">
                <a href="/seeds/list" class="material-button-text">
                    <span class="material-icons text-sm mr-2">arrow_back</span>
                    Back to Seeds
                </a>
                <button type="submit" class="material-button-filled">
                    <span class="material-icons text-sm mr-2">inventory</span>
                    Add Seed
                </button>
            </div>
        </form>
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle source type selection
    const sourceTextRadio = document.getElementById('source_text');
    const sourcePlantRadio = document.getElementById('source_plant');
    const sourceTextInput = document.getElementById('source_text_input');
    const sourcePlantInput = document.getElementById('source_plant_input');

    // Handle source type changes
    sourceTextRadio.addEventListener('change', function() {
        if (this.checked) {
            sourceTextInput.classList.remove('hidden');
            sourcePlantInput.classList.add('hidden');
        }
    });

    sourcePlantRadio.addEventListener('change', function() {
        if (this.checked) {
            sourceTextInput.classList.add('hidden');
            sourcePlantInput.classList.remove('hidden');
        }
    });



    function showSuccessMessage(message) {
        // Create a temporary success message
        const successDiv = document.createElement('div');
        successDiv.className = 'fixed top-4 right-4 bg-primary-600 text-white px-6 py-3 rounded-lg shadow-lg z-50';
        successDiv.textContent = message;
        document.body.appendChild(successDiv);

        setTimeout(() => {
            successDiv.remove();
        }, 3000);
    }

    function showErrorMessage(message) {
        // Create a temporary error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed top-4 right-4 bg-error-600 text-white px-6 py-3 rounded-lg shadow-lg z-50';
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);

        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }
});

function handleEnterKey(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        const form = event.target.closest('form');
        if (form) {
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton && !submitButton.disabled) {
                submitButton.click();
            }
        }
    }
}

function handleFormSubmit(event) {
    const nameInput = document.getElementById('name');
    const herba_idSelect = document.getElementById('herba_id');
    const acquisitionYear = document.getElementById('acquisition_year');
    const expirationYear = document.getElementById('expiration_year');

    if (!nameInput.value.trim()) {
        event.preventDefault();
        nameInput.focus();
        return false;
    }

    if (!herba_idSelect.value) {
        event.preventDefault();
        herba_idSelect.focus();
        return false;
    }

    if (!acquisitionYear.value || !expirationYear.value) {
        event.preventDefault();
        if (!acquisitionYear.value) acquisitionYear.focus();
        else expirationYear.focus();
        return false;
    }

    return true;
}
</script>
{% endblock %}
