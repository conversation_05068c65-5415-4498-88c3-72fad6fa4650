{% extends "base.html" %}
{% block title %}Seeds{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-display-small font-normal text-surface-900 dark:text-surface-100">Seed Inventory</h1>
            <p class="text-body-large text-surface-600 dark:text-surface-400 mt-2">Track your seed collection and viability</p>
        </div>
        <a href="/seeds/new" class="material-button-filled flex items-center gap-2">
            <span class="material-icons">add</span>
            Add New Seed
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-4 rounded-full bg-primary-100 dark:bg-primary-900">
                    <span class="material-icons text-2xl text-primary-600 dark:text-primary-300">inventory</span>
                </div>
                <div class="ml-6">
                    <p class="text-label-large font-medium text-surface-600 dark:text-surface-400">Total Seeds</p>
                    <p class="text-headline-small font-normal text-surface-900 dark:text-surface-100">{{ seeds|length }}</p>
                </div>
            </div>
        </div>

        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-4 rounded-full bg-secondary-100 dark:bg-secondary-900">
                    <span class="material-icons text-2xl text-secondary-600 dark:text-secondary-300">check_circle</span>
                </div>
                <div class="ml-6">
                    <p class="text-label-large font-medium text-surface-600 dark:text-surface-400">Fresh Seeds</p>
                    <p class="text-headline-small font-normal text-surface-900 dark:text-surface-100">{{ fresh_seeds_count }}</p>
                </div>
            </div>
        </div>

        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-4 rounded-full bg-tertiary-100 dark:bg-tertiary-900">
                    <span class="material-icons text-2xl text-tertiary-600 dark:text-tertiary-300">warning</span>
                </div>
                <div class="ml-6">
                    <p class="text-label-large font-medium text-surface-600 dark:text-surface-400">Expiring Soon</p>
                    <p class="text-headline-small font-normal text-surface-900 dark:text-surface-100">{{ expiring_soon_count }}</p>
                </div>
            </div>
        </div>

        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-4 rounded-full bg-error-100 dark:bg-error-900">
                    <span class="material-icons text-2xl text-error-600 dark:text-error-300">cancel</span>
                </div>
                <div class="ml-6">
                    <p class="text-label-large font-medium text-surface-600 dark:text-surface-400">Expired</p>
                    <p class="text-headline-small font-normal text-surface-900 dark:text-surface-100">{{ expired_seeds_count }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Seeds Table -->
    <div class="material-card-elevated overflow-hidden">
        <div class="px-6 py-4 border-b border-surface-200 dark:border-surface-700">
            <div class="flex justify-between items-center">
                <h2 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Seed Collection</h2>
                <div class="flex items-center space-x-4">
                    <button id="select-all-btn" class="material-button-text text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                        Select All
                    </button>
                    <button id="bulk-actions-btn" class="material-button-outlined" disabled>
                        Bulk Actions
                    </button>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-surface-200 dark:divide-surface-700">
                <thead class="bg-surface-100 dark:bg-surface-700">
                    <tr>
                        <th class="px-6 py-4 text-left text-label-small font-medium text-surface-500 dark:text-surface-300 uppercase tracking-wider">
                            <input type="checkbox" id="select-all-checkbox" class="material-checkbox">
                        </th>
                        <th class="px-6 py-4 text-left text-label-small font-medium text-surface-500 dark:text-surface-300 uppercase tracking-wider">
                            Seed
                        </th>
                        <th class="px-6 py-4 text-left text-label-small font-medium text-surface-500 dark:text-surface-300 uppercase tracking-wider">
                            Acquisition
                        </th>
                        <th class="px-6 py-4 text-left text-label-small font-medium text-surface-500 dark:text-surface-300 uppercase tracking-wider">
                            Expiration
                        </th>
                        <th class="px-6 py-4 text-left text-label-small font-medium text-surface-500 dark:text-surface-300 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-4 text-left text-label-small font-medium text-surface-500 dark:text-surface-300 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-surface-50 dark:bg-surface-800 divide-y divide-surface-200 dark:divide-surface-700">
                    {% for seed in seeds %}
                    <tr class="hover:bg-surface-100 dark:hover:bg-surface-700 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="item-checkbox material-checkbox" data-type="seed" data-id="{{ seed.id }}">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-amber-100 dark:bg-amber-900 flex items-center justify-center">
                                        <svg class="w-6 h-6 text-amber-600 dark:text-amber-300" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-sage-900 dark:text-sage-100">
                                        {{ seed.name }}
                                    </div>
                                    {% if seed.note %}
                                    <div class="text-sm text-sage-500 dark:text-sage-400">
                                        {% if seed.note|length > 50 %}{{ seed.note|slice(end=50) }}...{% else %}{{ seed.note }}{% endif %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sage-900 dark:text-sage-100">
                            {{ seed.acquisition_year }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sage-900 dark:text-sage-100">
                            {{ seed.expiration_year }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if seed.expiration_year < 2024 %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    Expired
                                </span>
                            {% elif seed.expiration_year == 2025 %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                    Expiring Soon
                                </span>
                            {% else %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    Fresh
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="/seeds/{{ seed.id }}/edit" class="text-sage-600 hover:text-sage-900 dark:text-sage-400 dark:hover:text-sage-300 mr-3">
                                Edit
                            </a>
                            <form method="post" action="/seeds/{{ seed.id }}/delete" class="inline" onsubmit="return confirm('Are you sure you want to delete this seed?')">
                                <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                    Delete
                                </button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% if seeds|length == 0 %}
        <div class="px-6 py-8 text-center">
            <p class="text-sage-500 dark:text-sage-400">No seeds found in your inventory.</p>
            <a href="/seeds/new" class="mt-2 inline-block text-sage-600 hover:text-sage-500 dark:text-sage-400 dark:hover:text-sage-300 font-medium">
                Add your first seed →
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const selectAllBtn = document.getElementById('select-all-btn');
    const bulkActionsBtn = document.getElementById('bulk-actions-btn');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');

    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        itemCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActionsButton();
    });

    selectAllBtn.addEventListener('click', function() {
        const allChecked = Array.from(itemCheckboxes).every(cb => cb.checked);
        itemCheckboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });
        selectAllCheckbox.checked = !allChecked;
        updateBulkActionsButton();
    });

    // Individual checkbox functionality
    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateBulkActionsButton();
        });
    });

    function updateSelectAllState() {
        const checkedCount = Array.from(itemCheckboxes).filter(cb => cb.checked).length;
        const totalCount = itemCheckboxes.length;

        selectAllCheckbox.checked = checkedCount === totalCount;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
    }

    function updateBulkActionsButton() {
        const checkedCount = Array.from(itemCheckboxes).filter(cb => cb.checked).length;
        bulkActionsBtn.disabled = checkedCount === 0;
        bulkActionsBtn.textContent = checkedCount > 0 ? `Bulk Actions (${checkedCount})` : 'Bulk Actions';
    }

    // Bulk actions functionality
    bulkActionsBtn.addEventListener('click', function() {
        const selectedItems = Array.from(itemCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => ({
                type: cb.dataset.type,
                id: cb.dataset.id
            }));

        if (selectedItems.length === 0) return;

        // Show bulk actions menu
        const menu = document.createElement('div');
        menu.className = 'absolute right-0 mt-2 w-48 bg-surface-50 dark:bg-surface-800 rounded-lg shadow-elevation-3 py-1 z-50 border border-surface-200 dark:border-surface-700';
        menu.innerHTML = `
            <button class="w-full text-left px-4 py-2 text-body-medium text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-700" onclick="bulkAddToWishlist()">
                <span class="material-icons text-sm mr-2">favorite</span>
                Add to Wishlist
            </button>
            <button class="w-full text-left px-4 py-2 text-body-medium text-error-600 dark:text-error-400 hover:bg-surface-100 dark:hover:bg-surface-700" onclick="bulkDelete()">
                <span class="material-icons text-sm mr-2">delete</span>
                Delete Selected
            </button>
        `;

        // Position menu
        const rect = bulkActionsBtn.getBoundingClientRect();
        menu.style.position = 'fixed';
        menu.style.top = rect.bottom + 'px';
        menu.style.right = (window.innerWidth - rect.right) + 'px';

        document.body.appendChild(menu);

        // Close menu when clicking outside
        setTimeout(() => {
            document.addEventListener('click', function closeMenu(e) {
                if (!menu.contains(e.target)) {
                    menu.remove();
                    document.removeEventListener('click', closeMenu);
                }
            });
        }, 0);

        // Store selected items for bulk actions
        window.selectedItems = selectedItems;
    });

    window.bulkAddToWishlist = function() {
        if (!window.selectedItems || window.selectedItems.length === 0) return;

        const seedIds = window.selectedItems.map(item => item.id);

        fetch('/seeds/bulk-add-to-wishlist', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ seed_ids: seedIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while adding seeds to wishlist');
        });
    };

    window.bulkDelete = function() {
        if (!window.selectedItems || window.selectedItems.length === 0) return;

        window.showConfirm(`Are you sure you want to delete ${window.selectedItems.length} selected seeds?`, function() {
            const seedIds = window.selectedItems.map(item => item.id);

            fetch('/seeds/bulk-delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ seed_ids: seedIds })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.showSuccess(data.message);
                    setTimeout(() => location.reload(), 1500);
                } else {
                    window.showError('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                window.showError('An error occurred while deleting seeds');
            });
        });
    };
});
</script>
{% endblock %}
