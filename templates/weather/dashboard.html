{% extends "base.html" %}

{% block title %}Weather Dashboard - Garden Planner{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-7xl">
    <!-- Header -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="material-headline text-surface-900 dark:text-surface-50">Weather Dashboard</h1>
            <p class="material-body mt-2">Monitor weather conditions and get plant care recommendations</p>
        </div>
        <div class="flex items-center space-x-4">
            <button id="refresh-weather" class="material-button-outlined">
                <span class="material-icons mr-2">refresh</span>
                Refresh
            </button>
            {% if user_context.is_admin %}
            <button id="create-weather-alerts" class="material-button-filled">
                <span class="material-icons mr-2">warning</span>
                Create Alerts
            </button>
            {% endif %}
        </div>
    </div>

    <!-- Location Input -->
    <div class="material-card p-6 mb-8">
        <h2 class="material-title mb-4">Location Settings</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label for="latitude" class="block text-label-large text-surface-700 dark:text-surface-200 mb-2">Latitude</label>
                <input type="number" id="latitude" step="0.0001" value="40.7128" class="material-input" placeholder="40.7128">
            </div>
            <div>
                <label for="longitude" class="block text-label-large text-surface-700 dark:text-surface-200 mb-2">Longitude</label>
                <input type="number" id="longitude" step="0.0001" value="-74.0060" class="material-input" placeholder="-74.0060">
            </div>
            <div class="flex items-end">
                <button id="get-location" class="material-button-outlined w-full">
                    <span class="material-icons mr-2">my_location</span>
                    Use My Location
                </button>
            </div>
        </div>
    </div>

    <!-- Current Weather -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <div class="lg:col-span-2">
            <div class="material-card-elevated p-6">
                <h2 class="material-title mb-6">Current Weather</h2>
                <div id="current-weather" class="text-center">
                    <div class="animate-pulse">
                        <div class="h-16 w-16 bg-surface-200 dark:bg-surface-700 rounded-full mx-auto mb-4"></div>
                        <div class="h-8 bg-surface-200 dark:bg-surface-700 rounded mb-2"></div>
                        <div class="h-4 bg-surface-200 dark:bg-surface-700 rounded"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div>
            <div class="material-card-elevated p-6">
                <h2 class="material-title mb-6">Weather Details</h2>
                <div id="weather-details" class="space-y-4">
                    <div class="animate-pulse space-y-4">
                        <div class="h-4 bg-surface-200 dark:bg-surface-700 rounded"></div>
                        <div class="h-4 bg-surface-200 dark:bg-surface-700 rounded"></div>
                        <div class="h-4 bg-surface-200 dark:bg-surface-700 rounded"></div>
                        <div class="h-4 bg-surface-200 dark:bg-surface-700 rounded"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Weather Alerts -->
    <div id="weather-alerts-container" class="mb-8 hidden">
        <div class="material-card p-6">
            <h2 class="material-title mb-4 text-error-600 dark:text-error-400">
                <span class="material-icons mr-2">warning</span>
                Weather Alerts
            </h2>
            <div id="weather-alerts" class="space-y-4"></div>
        </div>
    </div>

    <!-- Plant Care Recommendations -->
    <div class="material-card p-6 mb-8">
        <h2 class="material-title mb-4">
            <span class="material-icons mr-2 text-primary-600">eco</span>
            Plant Care Recommendations
        </h2>
        <div id="care-recommendations" class="space-y-3">
            <div class="animate-pulse space-y-3">
                <div class="h-4 bg-surface-200 dark:bg-surface-700 rounded"></div>
                <div class="h-4 bg-surface-200 dark:bg-surface-700 rounded w-3/4"></div>
                <div class="h-4 bg-surface-200 dark:bg-surface-700 rounded w-1/2"></div>
            </div>
        </div>
    </div>

    <!-- 5-Day Forecast -->
    <div class="material-card p-6">
        <h2 class="material-title mb-6">5-Day Forecast</h2>
        <div id="weather-forecast" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
            <!-- Forecast cards will be populated by JavaScript -->
            <div class="animate-pulse space-y-4">
                <div class="h-32 bg-surface-200 dark:bg-surface-700 rounded"></div>
                <div class="h-32 bg-surface-200 dark:bg-surface-700 rounded"></div>
                <div class="h-32 bg-surface-200 dark:bg-surface-700 rounded"></div>
                <div class="h-32 bg-surface-200 dark:bg-surface-700 rounded"></div>
                <div class="h-32 bg-surface-200 dark:bg-surface-700 rounded"></div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentLat = 40.7128;
    let currentLon = -74.0060;

    // Load weather data on page load
    loadWeatherData();

    // Event listeners
    document.getElementById('refresh-weather').addEventListener('click', loadWeatherData);
    document.getElementById('get-location').addEventListener('click', getCurrentLocation);
    
    document.getElementById('latitude').addEventListener('change', function() {
        currentLat = parseFloat(this.value);
        loadWeatherData();
    });
    
    document.getElementById('longitude').addEventListener('change', function() {
        currentLon = parseFloat(this.value);
        loadWeatherData();
    });

    {% if user_context.is_admin %}
    document.getElementById('create-weather-alerts').addEventListener('click', createWeatherAlerts);
    {% endif %}

    function getCurrentLocation() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(function(position) {
                currentLat = position.coords.latitude;
                currentLon = position.coords.longitude;
                document.getElementById('latitude').value = currentLat;
                document.getElementById('longitude').value = currentLon;
                loadWeatherData();
            }, function(error) {
                console.error('Error getting location:', error);
                alert('Unable to get your location. Please enter coordinates manually.');
            });
        } else {
            alert('Geolocation is not supported by this browser.');
        }
    }

    async function loadWeatherData() {
        try {
            const response = await fetch(`/weather/data?latitude=${currentLat}&longitude=${currentLon}`);
            const data = await response.json();

            if (data.error) {
                throw new Error(data.error);
            }

            displayCurrentWeather(data.current);
            displayWeatherDetails(data.current);
            displayWeatherAlerts(data.alerts);
            displayCareRecommendations(data.recommendations);
            displayForecast(data.forecast);

        } catch (error) {
            console.error('Error loading weather data:', error);
            document.getElementById('current-weather').innerHTML = `
                <div class="text-error-600 dark:text-error-400">
                    <span class="material-icons text-4xl mb-2">error</span>
                    <p>Failed to load weather data</p>
                </div>
            `;
        }
    }

    function displayCurrentWeather(weather) {
        const weatherIcon = getWeatherIcon(weather.icon);
        document.getElementById('current-weather').innerHTML = `
            <div class="text-6xl mb-4">${weatherIcon}</div>
            <div class="text-4xl font-bold text-surface-900 dark:text-surface-50 mb-2">
                ${Math.round(weather.temperature)}°C
            </div>
            <div class="text-xl text-surface-600 dark:text-surface-400 capitalize">
                ${weather.description}
            </div>
        `;
    }

    function displayWeatherDetails(weather) {
        document.getElementById('weather-details').innerHTML = `
            <div class="flex justify-between items-center">
                <span class="text-surface-600 dark:text-surface-400">Humidity</span>
                <span class="font-medium">${Math.round(weather.humidity)}%</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-surface-600 dark:text-surface-400">Pressure</span>
                <span class="font-medium">${Math.round(weather.pressure)} hPa</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-surface-600 dark:text-surface-400">Wind Speed</span>
                <span class="font-medium">${Math.round(weather.wind_speed * 3.6)} km/h</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-surface-600 dark:text-surface-400">Wind Direction</span>
                <span class="font-medium">${Math.round(weather.wind_direction)}°</span>
            </div>
        `;
    }

    function displayWeatherAlerts(alerts) {
        const container = document.getElementById('weather-alerts-container');
        const alertsDiv = document.getElementById('weather-alerts');

        if (alerts.length === 0) {
            container.classList.add('hidden');
            return;
        }

        container.classList.remove('hidden');
        alertsDiv.innerHTML = alerts.map(alert => `
            <div class="material-notification material-notification-${getSeverityClass(alert.severity)} p-4 rounded-lg">
                <div class="flex items-start">
                    <span class="material-icons text-xl mr-3 mt-1">${getSeverityIcon(alert.severity)}</span>
                    <div>
                        <h3 class="font-medium text-surface-900 dark:text-surface-50 mb-1">${alert.title}</h3>
                        <p class="text-surface-700 dark:text-surface-200">${alert.description}</p>
                    </div>
                </div>
            </div>
        `).join('');
    }

    function displayCareRecommendations(recommendations) {
        const container = document.getElementById('care-recommendations');
        
        if (recommendations.length === 0) {
            container.innerHTML = '<p class="text-surface-600 dark:text-surface-400">No specific recommendations at this time.</p>';
            return;
        }

        container.innerHTML = recommendations.map(rec => `
            <div class="flex items-start p-3 bg-primary-50 dark:bg-primary-950 rounded-lg">
                <span class="material-icons text-primary-600 dark:text-primary-400 mr-3 mt-1">lightbulb</span>
                <p class="text-surface-700 dark:text-surface-200">${rec}</p>
            </div>
        `).join('');
    }

    function displayForecast(forecast) {
        const container = document.getElementById('weather-forecast');
        
        // Group forecast by day (take first forecast of each day)
        const dailyForecast = [];
        const seenDates = new Set();
        
        for (const item of forecast) {
            const date = new Date(item.date).toDateString();
            if (!seenDates.has(date) && dailyForecast.length < 5) {
                seenDates.add(date);
                dailyForecast.push(item);
            }
        }

        container.innerHTML = dailyForecast.map(day => {
            const date = new Date(day.date);
            const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
            const weatherIcon = getWeatherIcon(day.icon);
            
            return `
                <div class="material-card p-4 text-center">
                    <div class="text-label-large font-medium text-surface-900 dark:text-surface-50 mb-2">${dayName}</div>
                    <div class="text-3xl mb-2">${weatherIcon}</div>
                    <div class="text-body-medium text-surface-700 dark:text-surface-200 mb-1">
                        ${Math.round(day.temperature_max)}° / ${Math.round(day.temperature_min)}°
                    </div>
                    <div class="text-body-small text-surface-600 dark:text-surface-400 capitalize">
                        ${day.description}
                    </div>
                    ${day.precipitation_chance > 30 ? `
                        <div class="text-body-small text-primary-600 dark:text-primary-400 mt-1">
                            ${Math.round(day.precipitation_chance)}% rain
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    function getWeatherIcon(iconCode) {
        const iconMap = {
            '01d': '☀️', '01n': '🌙',
            '02d': '⛅', '02n': '☁️',
            '03d': '☁️', '03n': '☁️',
            '04d': '☁️', '04n': '☁️',
            '09d': '🌧️', '09n': '🌧️',
            '10d': '🌦️', '10n': '🌧️',
            '11d': '⛈️', '11n': '⛈️',
            '13d': '❄️', '13n': '❄️',
            '50d': '🌫️', '50n': '🌫️'
        };
        return iconMap[iconCode] || '🌤️';
    }

    function getSeverityClass(severity) {
        const severityMap = {
            'minor': 'info',
            'moderate': 'warning',
            'severe': 'error',
            'extreme': 'error'
        };
        return severityMap[severity] || 'info';
    }

    function getSeverityIcon(severity) {
        const iconMap = {
            'minor': 'info',
            'moderate': 'warning',
            'severe': 'error',
            'extreme': 'dangerous'
        };
        return iconMap[severity] || 'info';
    }

    {% if user_context.is_admin %}
    async function createWeatherAlerts() {
        try {
            const response = await fetch(`/weather/create_alerts?latitude=${currentLat}&longitude=${currentLon}`, {
                method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
                window.showSuccess(data.message);
            } else {
                throw new Error(data.error || 'Failed to create weather alerts');
            }
        } catch (error) {
            console.error('Error creating weather alerts:', error);
            window.showError('Failed to create weather alerts: ' + error.message);
        }
    }
    {% endif %}
});
</script>
{% endblock %}
