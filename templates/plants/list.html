{% extends "base.html" %}
{% block title %}Plants{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-display-small font-normal text-surface-900 dark:text-surface-100">Plant Database</h1>
            <p class="text-body-large text-surface-600 dark:text-surface-400 mt-2">Manage your personal plant collection</p>
        </div>
        <a href="/plants/new" class="material-button-filled flex items-center gap-2">
            <span class="material-icons">add</span>
            Add New Plant
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-4 rounded-full bg-primary-100 dark:bg-primary-900">
                    <span class="material-icons text-2xl text-primary-600 dark:text-primary-300">local_florist</span>
                </div>
                <div class="ml-6">
                    <p class="text-label-large font-medium text-surface-600 dark:text-surface-400">Total Plants</p>
                    <p class="text-headline-small font-normal text-surface-900 dark:text-surface-100">{{ plants|length }}</p>
                </div>
            </div>
        </div>

        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-4 rounded-full bg-secondary-100 dark:bg-secondary-900">
                    <span class="material-icons text-2xl text-secondary-600 dark:text-secondary-300">science</span>
                </div>
                <div class="ml-6">
                    <p class="text-label-large font-medium text-surface-600 dark:text-surface-400">With Latin Names</p>
                    <p class="text-headline-small font-normal text-surface-900 dark:text-surface-100">{{ plants_with_latin_names }}</p>
                </div>
            </div>
        </div>

        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-4 rounded-full bg-tertiary-100 dark:bg-tertiary-900">
                    <span class="material-icons text-2xl text-tertiary-600 dark:text-tertiary-300">category</span>
                </div>
                <div class="ml-6">
                    <p class="text-label-large font-medium text-surface-600 dark:text-surface-400">Varieties</p>
                    <p class="text-headline-small font-normal text-surface-900 dark:text-surface-100">{{ plants_with_varieties }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Plants Table -->
    <div class="material-card-elevated overflow-hidden">
        <div class="px-6 py-4 border-b border-surface-200 dark:border-surface-700">
            <div class="flex justify-between items-center">
                <h2 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Plant Collection</h2>
                <div class="flex items-center space-x-4">
                    <button id="select-all-btn" class="material-button-text text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                        Select All
                    </button>
                    <button id="bulk-actions-btn" class="material-button-outlined" disabled>
                        Bulk Actions
                    </button>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-surface-200 dark:divide-surface-700">
                <thead class="bg-surface-100 dark:bg-surface-700">
                    <tr>
                        <th class="px-6 py-4 text-left text-label-small font-medium text-surface-500 dark:text-surface-300 uppercase tracking-wider">
                            <input type="checkbox" id="select-all-checkbox" class="material-checkbox">
                        </th>
                        <th class="px-6 py-4 text-left text-label-small font-medium text-surface-500 dark:text-surface-300 uppercase tracking-wider">
                            Plant
                        </th>
                        <th class="px-6 py-4 text-left text-label-small font-medium text-surface-500 dark:text-surface-300 uppercase tracking-wider">
                            Latin Name
                        </th>
                        <th class="px-6 py-4 text-left text-label-small font-medium text-surface-500 dark:text-surface-300 uppercase tracking-wider">
                            Variety
                        </th>
                        <th class="px-6 py-4 text-left text-label-small font-medium text-surface-500 dark:text-surface-300 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-surface-50 dark:bg-surface-800 divide-y divide-surface-200 dark:divide-surface-700">
                    {% for plant in plants %}
                    <tr class="hover:bg-surface-100 dark:hover:bg-surface-700 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="item-checkbox material-checkbox" data-type="plant" data-id="{{ plant.id }}">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-12 w-12">
                                    <div class="h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                                        <span class="material-icons text-xl text-primary-600 dark:text-primary-300">local_florist</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-body-medium font-medium text-surface-900 dark:text-surface-100">
                                        {{ plant.name }}
                                    </div>
                                    {% if plant.note %}
                                    <div class="text-body-small text-surface-500 dark:text-surface-400">
                                        {% if plant.note|length > 50 %}{{ plant.note|slice(end=50) }}...{% else %}{{ plant.note }}{% endif %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-body-medium text-surface-900 dark:text-surface-100">
                            {{ plant.latin_name | default(value="Not specified") }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-body-medium text-surface-900 dark:text-surface-100">
                            {{ plant.variety | default(value="Standard") }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-body-medium font-medium">
                            <a href="/plants/{{ plant.id }}/edit" class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-4">
                                Edit
                            </a>
                            <form method="post" action="/plants/{{ plant.id }}/delete" class="inline" onsubmit="return confirm('Are you sure you want to delete this plant?')">
                                <button type="submit" class="text-error-600 hover:text-error-900 dark:text-error-400 dark:hover:text-error-300">
                                    Delete
                                </button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% if plants|length == 0 %}
        <div class="px-6 py-12 text-center">
            <span class="material-icons text-6xl text-surface-400 dark:text-surface-600 mb-4 block">local_florist</span>
            <p class="text-body-large text-surface-500 dark:text-surface-400 mb-4">No plants found in your collection.</p>
            <a href="/plants/new" class="material-button-text">
                Add your first plant
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const selectAllBtn = document.getElementById('select-all-btn');
    const bulkActionsBtn = document.getElementById('bulk-actions-btn');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');

    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        itemCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActionsButton();
    });

    selectAllBtn.addEventListener('click', function() {
        const allChecked = Array.from(itemCheckboxes).every(cb => cb.checked);
        itemCheckboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });
        selectAllCheckbox.checked = !allChecked;
        updateBulkActionsButton();
    });

    // Individual checkbox functionality
    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateBulkActionsButton();
        });
    });

    function updateSelectAllState() {
        const checkedCount = Array.from(itemCheckboxes).filter(cb => cb.checked).length;
        const totalCount = itemCheckboxes.length;

        selectAllCheckbox.checked = checkedCount === totalCount;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
    }

    function updateBulkActionsButton() {
        const checkedCount = Array.from(itemCheckboxes).filter(cb => cb.checked).length;
        bulkActionsBtn.disabled = checkedCount === 0;
        bulkActionsBtn.textContent = checkedCount > 0 ? `Bulk Actions (${checkedCount})` : 'Bulk Actions';
    }

    // Bulk actions functionality
    bulkActionsBtn.addEventListener('click', function() {
        const selectedItems = Array.from(itemCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => ({
                type: cb.dataset.type,
                id: cb.dataset.id
            }));

        if (selectedItems.length === 0) return;

        // Show bulk actions menu
        const menu = document.createElement('div');
        menu.className = 'absolute right-0 mt-2 w-48 bg-surface-50 dark:bg-surface-800 rounded-lg shadow-elevation-3 py-1 z-50 border border-surface-200 dark:border-surface-700';
        menu.innerHTML = `
            <button class="w-full text-left px-4 py-2 text-body-medium text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-700" onclick="bulkAddToWishlist()">
                <span class="material-icons text-sm mr-2">favorite</span>
                Add to Wishlist
            </button>
            <button class="w-full text-left px-4 py-2 text-body-medium text-error-600 dark:text-error-400 hover:bg-surface-100 dark:hover:bg-surface-700" onclick="bulkDelete()">
                <span class="material-icons text-sm mr-2">delete</span>
                Delete Selected
            </button>
        `;

        // Position menu
        const rect = bulkActionsBtn.getBoundingClientRect();
        menu.style.position = 'fixed';
        menu.style.top = rect.bottom + 'px';
        menu.style.right = (window.innerWidth - rect.right) + 'px';

        document.body.appendChild(menu);

        // Close menu when clicking outside
        setTimeout(() => {
            document.addEventListener('click', function closeMenu(e) {
                if (!menu.contains(e.target)) {
                    menu.remove();
                    document.removeEventListener('click', closeMenu);
                }
            });
        }, 0);

        // Store selected items for bulk actions
        window.selectedItems = selectedItems;
    });

    window.bulkAddToWishlist = function() {
        if (!window.selectedItems || window.selectedItems.length === 0) return;

        // Add each selected item to wishlist
        window.selectedItems.forEach(item => {
            const form = document.createElement('form');
            form.method = 'post';
            form.action = '/wishlist/add';
            form.innerHTML = `
                <input type="hidden" name="item_type" value="${item.type}">
                <input type="hidden" name="item_id" value="${item.id}">
            `;
            document.body.appendChild(form);
            form.submit();
        });
    };

    window.bulkDelete = function() {
        if (!window.selectedItems || window.selectedItems.length === 0) return;

        window.showConfirm(`Are you sure you want to delete ${window.selectedItems.length} selected plants?`, function() {
            // Delete each selected item
            window.selectedItems.forEach(item => {
                const form = document.createElement('form');
                form.method = 'post';
                form.action = `/plants/${item.id}/delete`;
                document.body.appendChild(form);
                form.submit();
            });
        });
    };
});
</script>
{% endblock %}
