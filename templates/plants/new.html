{% extends "base.html" %}
{% block title %}Add New Plant - Garden Planner{% endblock %}
{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="material-card-elevated p-8">
        <div class="text-center mb-8">
            <span class="material-icons text-5xl text-primary-600 dark:text-primary-400 mb-4 block">local_florist</span>
            <h1 class="text-headline-medium font-normal text-surface-900 dark:text-surface-100 mb-2">Add New Plant</h1>
            <p class="text-body-large text-surface-600 dark:text-surface-400">Add a new plant to your collection</p>
        </div>

        <form method="post" action="/plants/create" class="space-y-6" onsubmit="return handleFormSubmit(event)">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

            <!-- Plant Name -->
            <div>
                <label for="name" class="material-label">Plant Name *</label>
                <input type="text" id="name" name="name" required
                       class="material-input" placeholder="e.g., My Cherry Tomato Plant"
                       onkeypress="handleEnterKey(event)">
                <div class="material-helper-text">Give your plant a unique name</div>
            </div>

            <!-- HerbaDB Selection -->
            <div>
                <label for="herba_plant_id" class="material-label">HerbaDB Entry *</label>
                <select id="herba_plant_id" name="herba_plant_id" required class="material-select">
                    <option value="">Select a HerbaDB entry...</option>
                    {% for herba_plant in herba_plants %}
                    <option value="{{ herba_plant.id }}">{{ herba_plant.common_name }} ({{ herba_plant.latin_name }})</option>
                    {% endfor %}
                </select>
                <div class="material-helper-text">Choose the plant type from HerbaDB</div>
            </div>

            <!-- Variety -->
            <div>
                <label for="variety" class="material-label">Variety (Optional)</label>
                <input type="text" id="variety" name="variety"
                       class="material-input" placeholder="e.g., Cherry Red, Beefsteak"
                       onkeypress="handleEnterKey(event)">
                <div class="material-helper-text">Specific variety or cultivar</div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="material-label">Description (Optional)</label>
                <textarea id="description" name="description" rows="3"
                          class="material-textarea" placeholder="Describe your plant..."></textarea>
                <div class="material-helper-text">Any additional details about this plant</div>
            </div>

            <!-- Care Tracking Fields -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="last_watering_date" class="material-label">Last Watering Date</label>
                    <input type="date" id="last_watering_date" name="last_watering_date"
                           class="material-input" onkeypress="handleEnterKey(event)">
                    <div class="material-helper-text">When did you last water this plant?</div>
                </div>
                <div>
                    <label for="last_fertilizing_date" class="material-label">Last Fertilizing Date</label>
                    <input type="date" id="last_fertilizing_date" name="last_fertilizing_date"
                           class="material-input" onkeypress="handleEnterKey(event)">
                    <div class="material-helper-text">When did you last fertilize?</div>
                </div>
                <div>
                    <label for="last_repotting_date" class="material-label">Last Repotting Date</label>
                    <input type="date" id="last_repotting_date" name="last_repotting_date"
                           class="material-input" onkeypress="handleEnterKey(event)">
                    <div class="material-helper-text">When did you last repot?</div>
                </div>
            </div>

            <!-- Notes -->
            <div>
                <label for="note" class="material-label">Notes (Optional)</label>
                <textarea id="note" name="note" rows="3"
                          class="material-textarea" placeholder="Any care notes, observations, or reminders..."></textarea>
                <div class="material-helper-text">Care instructions, observations, etc.</div>
            </div>

            <div class="flex justify-between items-center pt-6">
                <a href="/plants/list" class="material-button-text">
                    <span class="material-icons text-sm mr-2">arrow_back</span>
                    Back to Plants
                </a>
                <button type="submit" class="material-button-filled">
                    <span class="material-icons text-sm mr-2">local_florist</span>
                    Add Plant
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function handleEnterKey(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        const form = event.target.closest('form');
        if (form) {
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton && !submitButton.disabled) {
                submitButton.click();
            }
        }
    }
}

function handleFormSubmit(event) {
    const nameInput = document.getElementById('name');
    const herba_plant_idSelect = document.getElementById('herba_plant_id');

    if (!nameInput.value.trim()) {
        event.preventDefault();
        nameInput.focus();
        return false;
    }

    if (!herba_plant_idSelect.value) {
        event.preventDefault();
        herba_plant_idSelect.focus();
        return false;
    }

    return true;
}
</script>
{% endblock %}
