{% extends "base.html" %}
{% block title %}Admin Settings - <PERSON> Planner{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto">
    <div class="text-center mb-8">
        <span class="material-icons text-5xl text-primary-600 dark:text-primary-400 mb-4 block">settings</span>
        <h1 class="text-display-small font-normal text-surface-900 dark:text-surface-100 mb-2">System Settings</h1>
        <p class="text-body-large text-surface-600 dark:text-surface-400">Configure system-wide settings and preferences</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- General Settings -->
        <div class="material-card-elevated p-6">
            <div class="flex items-center mb-6">
                <span class="material-icons text-2xl text-primary-600 dark:text-primary-400 mr-3">tune</span>
                <h2 class="text-title-large font-medium text-surface-900 dark:text-surface-100">General Settings</h2>
            </div>
            
            <div class="space-y-6">
                <div>
                    <label class="material-label">Application Name</label>
                    <input type="text" value="Garden Planner" class="material-input" readonly>
                    <div class="material-helper-text">The name displayed throughout the application</div>
                </div>
                
                <div>
                    <label class="material-label">Default Theme</label>
                    <select class="material-select">
                        <option value="auto">Auto (System)</option>
                        <option value="light">Light Mode</option>
                        <option value="dark">Dark Mode</option>
                    </select>
                    <div class="material-helper-text">Default theme for new users</div>
                </div>
                
                <div>
                    <label class="material-label">Registration</label>
                    <select class="material-select">
                        <option value="open">Open Registration</option>
                        <option value="admin">Admin Only</option>
                        <option value="closed">Closed</option>
                    </select>
                    <div class="material-helper-text">Control who can register new accounts</div>
                </div>
            </div>
        </div>

        <!-- Notification Settings -->
        <div class="material-card-elevated p-6">
            <div class="flex items-center mb-6">
                <span class="material-icons text-2xl text-secondary-600 dark:text-secondary-400 mr-3">notifications</span>
                <h2 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Notification Settings</h2>
            </div>
            
            <div class="space-y-6">
                <div>
                    <label class="material-label">Default Watering Reminder</label>
                    <select class="material-select">
                        <option value="3">Every 3 days</option>
                        <option value="5" selected>Every 5 days</option>
                        <option value="7">Every 7 days</option>
                        <option value="14">Every 14 days</option>
                    </select>
                    <div class="material-helper-text">Default watering reminder interval</div>
                </div>
                
                <div>
                    <label class="material-label">Default Fertilizing Reminder</label>
                    <select class="material-select">
                        <option value="14">Every 2 weeks</option>
                        <option value="21" selected>Every 3 weeks</option>
                        <option value="30">Every month</option>
                        <option value="60">Every 2 months</option>
                    </select>
                    <div class="material-helper-text">Default fertilizing reminder interval</div>
                </div>
                
                <div class="flex items-center space-x-3">
                    <input type="checkbox" id="email-notifications" class="w-5 h-5 text-primary-600 rounded">
                    <label for="email-notifications" class="text-body-medium text-surface-700 dark:text-surface-300">
                        Enable email notifications (future feature)
                    </label>
                </div>
            </div>
        </div>

        <!-- HerbaDB Settings -->
        <div class="material-card-elevated p-6">
            <div class="flex items-center mb-6">
                <span class="material-icons text-2xl text-tertiary-600 dark:text-tertiary-400 mr-3">local_florist</span>
                <h2 class="text-title-large font-medium text-surface-900 dark:text-surface-100">HerbaDB Settings</h2>
            </div>
            
            <div class="space-y-6">
                <div>
                    <label class="material-label">Auto-populate Plant Data</label>
                    <select class="material-select">
                        <option value="enabled" selected>Enabled</option>
                        <option value="disabled">Disabled</option>
                    </select>
                    <div class="material-helper-text">Automatically gather plant information when creating new plants</div>
                </div>
                
                <div>
                    <label class="material-label">Scraping Timeout (seconds)</label>
                    <input type="number" value="30" min="10" max="120" class="material-input">
                    <div class="material-helper-text">Maximum time to wait for plant data scraping</div>
                </div>
                
                <div class="flex items-center space-x-3">
                    <input type="checkbox" id="cache-herba-data" class="w-5 h-5 text-primary-600 rounded" checked>
                    <label for="cache-herba-data" class="text-body-medium text-surface-700 dark:text-surface-300">
                        Cache scraped plant data
                    </label>
                </div>
            </div>
        </div>

        <!-- System Statistics -->
        <div class="material-card-elevated p-6">
            <div class="flex items-center mb-6">
                <span class="material-icons text-2xl text-primary-600 dark:text-primary-400 mr-3">analytics</span>
                <h2 class="text-title-large font-medium text-surface-900 dark:text-surface-100">System Statistics</h2>
            </div>
            
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-body-medium text-surface-600 dark:text-surface-400">Total Users:</span>
                    <span class="text-body-medium font-semibold text-surface-900 dark:text-surface-100">{{ total_users }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-body-medium text-surface-600 dark:text-surface-400">Total Plants:</span>
                    <span class="text-body-medium font-semibold text-surface-900 dark:text-surface-100">{{ total_plants }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-body-medium text-surface-600 dark:text-surface-400">Application Version:</span>
                    <span class="text-body-medium font-semibold text-surface-900 dark:text-surface-100">v0.1.0</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-body-medium text-surface-600 dark:text-surface-400">Database Engine:</span>
                    <span class="text-body-medium font-semibold text-surface-900 dark:text-surface-100">SQLite</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-8 flex justify-between items-center">
        <a href="/admin/dashboard" class="material-button-text">
            <span class="material-icons text-sm mr-2">arrow_back</span>
            Back to Dashboard
        </a>
        <div class="space-x-4">
            <button type="button" class="material-button-outlined" onclick="resetSettings()">
                <span class="material-icons text-sm mr-2">refresh</span>
                Reset to Defaults
            </button>
            <button type="button" class="material-button-filled" onclick="saveSettings()">
                <span class="material-icons text-sm mr-2">save</span>
                Save Settings
            </button>
        </div>
    </div>
</div>

<script>
function saveSettings() {
    window.showInfo('Settings functionality will be implemented in a future update.');
}

function resetSettings() {
    window.showConfirm('Are you sure you want to reset all settings to their default values?', function() {
        window.showInfo('Reset functionality will be implemented in a future update.');
    });
}
</script>
{% endblock %}
