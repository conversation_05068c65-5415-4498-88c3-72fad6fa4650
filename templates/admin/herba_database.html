{% extends "base.html" %}
{% block title %}HerbaDB Management{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-headline-medium font-normal text-surface-900 dark:text-surface-100">HerbaDB Management</h1>
            <p class="text-body-medium text-surface-600 dark:text-surface-400 mt-2">Manage global plant database and botanical information</p>
        </div>
        <a href="/admin" class="material-button-filled">
            Back to Dashboard
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-primary-100 dark:bg-primary-900">
                    <span class="material-icons text-xl text-primary-600 dark:text-primary-300">local_florist</span>
                </div>
                <div class="ml-4">
                    <p class="text-label-medium text-surface-600 dark:text-surface-400">Total Plants</p>
                    <p class="text-display-small font-normal text-surface-900 dark:text-surface-100">{{ plants|length }}</p>
                </div>
            </div>
        </div>

        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-secondary-100 dark:bg-secondary-900">
                    <span class="material-icons text-xl text-secondary-600 dark:text-secondary-300">eco</span>
                </div>
                <div class="ml-4">
                    <p class="text-label-medium text-surface-600 dark:text-surface-400">HerbaDB Entries</p>
                    <p class="text-display-small font-normal text-surface-900 dark:text-surface-100">{{ herba_plants|length }}</p>
                </div>
            </div>
        </div>

        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-tertiary-100 dark:bg-tertiary-900">
                    <span class="material-icons text-xl text-tertiary-600 dark:text-tertiary-300">verified</span>
                </div>
                <div class="ml-4">
                    <p class="text-label-medium text-surface-600 dark:text-surface-400">Verified Entries</p>
                    <p class="text-display-small font-normal text-surface-900 dark:text-surface-100">{{ herba_plants|length }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Management Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div class="material-card p-6">
            <h3 class="text-title-large font-normal text-surface-900 dark:text-surface-100 mb-4">Add New Plant</h3>
            <p class="text-body-medium text-surface-600 dark:text-surface-300 mb-4">Add a new plant to the global HerbaDB</p>
            <a href="/plants/new" class="material-button-filled">
                Add Plant
            </a>
        </div>

        <div class="material-card p-6">
            <h3 class="text-title-large font-normal text-surface-900 dark:text-surface-100 mb-4">Scrape Plant Info</h3>
            <p class="text-body-medium text-surface-600 dark:text-surface-300 mb-4">Automatically gather plant information from public databases</p>
            <form method="post" action="/admin/herba-db/scrape" class="space-y-3">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="text" name="plant_name" placeholder="Enter plant name..." class="material-input" required>
                <button type="submit" class="w-full material-button-filled">
                    Scrape Plant Info
                </button>
            </form>
        </div>

        <div class="material-card p-6">
            <h3 class="text-title-large font-normal text-surface-900 dark:text-surface-100 mb-4">Export Database</h3>
            <p class="text-body-medium text-surface-600 dark:text-surface-300 mb-4">Export HerbaDB for backup or analysis</p>
            <a href="/admin/herba-db/export" class="material-button-outlined">
                Export Data
            </a>
        </div>
    </div>

    <!-- Plants Table -->
    <div class="material-card-elevated overflow-hidden">
        <div class="px-6 py-4 border-b border-surface-200 dark:border-surface-700">
            <div class="flex justify-between items-center">
                <h2 class="text-title-large font-normal text-surface-900 dark:text-surface-100">Plant Database</h2>
                <div class="flex items-center space-x-4">
                    <button id="select-all-btn" class="material-button-text text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                        Select All
                    </button>
                    <button id="bulk-actions-btn" class="material-button-outlined" disabled>
                        Bulk Actions
                    </button>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-surface-200 dark:divide-surface-700">
                <thead class="bg-surface-50 dark:bg-surface-800">
                    <tr>
                        <th class="px-6 py-3 text-left text-label-small text-surface-600 dark:text-surface-300 uppercase tracking-wider">
                            <input type="checkbox" id="select-all-checkbox" class="rounded border-surface-300 dark:border-surface-600 text-primary-600 focus:ring-primary-500 focus:ring-2">
                        </th>
                        <th class="px-6 py-3 text-left text-label-small text-surface-600 dark:text-surface-300 uppercase tracking-wider">
                            Plant
                        </th>
                        <th class="px-6 py-3 text-left text-label-small text-surface-600 dark:text-surface-300 uppercase tracking-wider">
                            Scientific Name
                        </th>
                        <th class="px-6 py-3 text-left text-label-small text-surface-600 dark:text-surface-300 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-label-small text-surface-600 dark:text-surface-300 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-surface-800 divide-y divide-surface-200 dark:divide-surface-700">
                    <!-- Regular Plants -->
                    {% for plant in plants %}
                    <tr class="hover:bg-surface-50 dark:hover:bg-surface-700 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="item-checkbox rounded border-surface-300 dark:border-surface-600 text-primary-600 focus:ring-primary-500 focus:ring-2" data-type="plant" data-id="{{ plant.id }}">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                                        <span class="material-icons text-lg text-primary-600 dark:text-primary-300">local_florist</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-body-medium font-medium text-surface-900 dark:text-surface-100">
                                        {{ plant.name }}
                                        <span class="ml-2 inline-flex px-2 py-1 text-label-small rounded-full bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                                            Plant
                                        </span>
                                    </div>
                                    <div class="text-body-small text-surface-600 dark:text-surface-400">
                                        {{ plant.common_name | default(value="No common name") }}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-body-medium text-surface-900 dark:text-surface-100">
                            {{ plant.scientific_name | default(value="Not specified") }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-label-small rounded-full bg-tertiary-100 text-tertiary-800 dark:bg-tertiary-900 dark:text-tertiary-200">
                                Available
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-body-medium font-medium">
                            <div class="flex items-center space-x-2">
                                <a href="/plants/{{ plant.id }}/edit" class="material-button-text text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                    Edit
                                </a>
                                <form method="post" action="/wishlist/add" class="inline">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                                    <input type="hidden" name="item_type" value="plant">
                                    <input type="hidden" name="item_id" value="{{ plant.id }}">
                                    <button type="submit" class="material-button-text text-secondary-600 hover:text-secondary-700 dark:text-secondary-400 dark:hover:text-secondary-300">
                                        Add to Wishlist
                                    </button>
                                </form>
                                <form method="post" action="/admin/plants/{{ plant.id }}/delete" class="inline" onsubmit="return confirm('Are you sure you want to delete this plant?')">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                                    <button type="submit" class="material-button-text text-error-600 hover:text-error-700 dark:text-error-400 dark:hover:text-error-300">
                                        Delete
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}

                    <!-- HerbaDB Plants -->
                    {% for herba_plant in herba_plants %}
                    <tr class="hover:bg-surface-50 dark:hover:bg-surface-700 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="item-checkbox rounded border-surface-300 dark:border-surface-600 text-primary-600 focus:ring-primary-500 focus:ring-2" data-type="herba_plant" data-id="{{ herba_plant.id }}">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-secondary-100 dark:bg-secondary-900 flex items-center justify-center">
                                        <span class="material-icons text-lg text-secondary-600 dark:text-secondary-300">eco</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-body-medium font-medium text-surface-900 dark:text-surface-100">
                                        {{ herba_plant.common_name }}
                                        <span class="ml-2 inline-flex px-2 py-1 text-label-small rounded-full bg-secondary-100 text-secondary-800 dark:bg-secondary-900 dark:text-secondary-200">
                                            HerbaDB
                                        </span>
                                    </div>
                                    <div class="text-body-small text-surface-600 dark:text-surface-400">
                                        {{ herba_plant.description | default(value="No description") | truncate(length=50) }}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-body-medium text-surface-900 dark:text-surface-100">
                            {{ herba_plant.latin_name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-label-small rounded-full bg-tertiary-100 text-tertiary-800 dark:bg-tertiary-900 dark:text-tertiary-200">
                                Available
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-body-medium font-medium">
                            <div class="flex items-center space-x-2">
                                <a href="/admin/herba-db/{{ herba_plant.id }}/edit" class="material-button-text text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                    Edit
                                </a>
                                <form method="post" action="/wishlist/add" class="inline">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                                    <input type="hidden" name="item_type" value="herba_plant">
                                    <input type="hidden" name="item_id" value="{{ herba_plant.id }}">
                                    <button type="submit" class="material-button-text text-secondary-600 hover:text-secondary-700 dark:text-secondary-400 dark:hover:text-secondary-300">
                                        Add to Wishlist
                                    </button>
                                </form>
                                <form method="post" action="/admin/herba-db/{{ herba_plant.id }}/delete" class="inline" onsubmit="return confirm('Are you sure you want to delete this HerbaDB entry?')">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                                    <button type="submit" class="material-button-text text-error-600 hover:text-error-700 dark:text-error-400 dark:hover:text-error-300">
                                        Delete
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% if plants|length == 0 and herba_plants|length == 0 %}
        <div class="px-6 py-8 text-center">
            <p class="text-body-medium text-surface-500 dark:text-surface-400">No plants found in the database. Use the scraper to add plant information.</p>
        </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const selectAllBtn = document.getElementById('select-all-btn');
    const bulkActionsBtn = document.getElementById('bulk-actions-btn');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');

    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        itemCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActionsButton();
    });

    selectAllBtn.addEventListener('click', function() {
        const allChecked = Array.from(itemCheckboxes).every(cb => cb.checked);
        itemCheckboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });
        selectAllCheckbox.checked = !allChecked;
        updateBulkActionsButton();
    });

    // Individual checkbox functionality
    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateBulkActionsButton();
        });
    });

    function updateSelectAllState() {
        const checkedCount = Array.from(itemCheckboxes).filter(cb => cb.checked).length;
        const totalCount = itemCheckboxes.length;

        selectAllCheckbox.checked = checkedCount === totalCount;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
    }

    function updateBulkActionsButton() {
        const checkedCount = Array.from(itemCheckboxes).filter(cb => cb.checked).length;
        bulkActionsBtn.disabled = checkedCount === 0;
        bulkActionsBtn.textContent = checkedCount > 0 ? `Bulk Actions (${checkedCount})` : 'Bulk Actions';
    }

    // Bulk actions functionality
    bulkActionsBtn.addEventListener('click', function() {
        const selectedItems = Array.from(itemCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => ({
                type: cb.dataset.type,
                id: cb.dataset.id
            }));

        if (selectedItems.length === 0) return;

        // Show bulk actions menu
        const menu = document.createElement('div');
        menu.className = 'absolute right-0 mt-2 w-48 bg-white dark:bg-surface-800 rounded-lg shadow-elevation-3 py-1 z-50 border border-surface-200 dark:border-surface-700';
        menu.innerHTML = `
            <button class="w-full text-left px-4 py-2 text-body-medium text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-700" onclick="bulkAddToWishlist()">
                Add to Wishlist
            </button>
            <button class="w-full text-left px-4 py-2 text-body-medium text-error-600 dark:text-error-400 hover:bg-surface-100 dark:hover:bg-surface-700" onclick="bulkDelete()">
                Delete Selected
            </button>
        `;

        // Position menu
        const rect = bulkActionsBtn.getBoundingClientRect();
        menu.style.position = 'fixed';
        menu.style.top = rect.bottom + 'px';
        menu.style.right = (window.innerWidth - rect.right) + 'px';

        document.body.appendChild(menu);

        // Close menu when clicking outside
        setTimeout(() => {
            document.addEventListener('click', function closeMenu(e) {
                if (!menu.contains(e.target)) {
                    menu.remove();
                    document.removeEventListener('click', closeMenu);
                }
            });
        }, 0);

        // Store selected items for bulk actions
        window.selectedItems = selectedItems;
    });

    window.bulkAddToWishlist = function() {
        if (!window.selectedItems || window.selectedItems.length === 0) return;

        // Add each selected item to wishlist
        window.selectedItems.forEach(item => {
            const form = document.createElement('form');
            form.method = 'post';
            form.action = '/wishlist/add';
            form.innerHTML = `
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" name="item_type" value="${item.type}">
                <input type="hidden" name="item_id" value="${item.id}">
            `;
            document.body.appendChild(form);
            form.submit();
        });
    };

    window.bulkDelete = function() {
        if (!window.selectedItems || window.selectedItems.length === 0) return;

        window.showConfirm(`Are you sure you want to delete ${window.selectedItems.length} selected items?`, function() {
            // Delete each selected item
            window.selectedItems.forEach(item => {
                const form = document.createElement('form');
                form.method = 'post';
                if (item.type === 'plant') {
                    form.action = `/admin/plants/${item.id}/delete`;
                } else {
                    form.action = `/admin/herba-db/${item.id}/delete`;
                }
                form.innerHTML = `<input type="hidden" name="csrf_token" value="{{ csrf_token }}">`;
                document.body.appendChild(form);
                form.submit();
            });
        });
    };
});
</script>
{% endblock %}
