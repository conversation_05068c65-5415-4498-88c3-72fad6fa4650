{% extends "base.html" %}
{% block title %}Admin Dashboard{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="text-center mb-8">
        <span class="material-icons text-5xl text-primary-600 dark:text-primary-400 mb-4 block">admin_panel_settings</span>
        <h1 class="text-display-small font-normal text-surface-900 dark:text-surface-100">Admin Dashboard</h1>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- User Management -->
        <div class="material-card p-6">
            <div class="flex items-center mb-4">
                <span class="material-icons text-2xl text-primary-600 dark:text-primary-400 mr-3">people</span>
                <h2 class="text-title-large font-medium text-surface-900 dark:text-surface-100">User Management</h2>
            </div>
            <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Manage users, roles, and permissions</p>
            <a href="/admin/users" class="material-button-filled">
                Manage Users
            </a>
        </div>

        <!-- System Statistics -->
        <div class="material-card p-6">
            <div class="flex items-center mb-4">
                <span class="material-icons text-2xl text-secondary-600 dark:text-secondary-400 mr-3">analytics</span>
                <h2 class="text-title-large font-medium text-surface-900 dark:text-surface-100">System Statistics</h2>
            </div>
            <div class="grid grid-cols-2 gap-4">
                <div class="text-center p-3 bg-surface-50 dark:bg-surface-700 rounded-lg">
                    <div class="text-display-small font-normal text-primary-600 dark:text-primary-400">{{ total_users | default(value="0") }}</div>
                    <div class="text-body-small text-surface-600 dark:text-surface-400">Users</div>
                </div>
                <div class="text-center p-3 bg-surface-50 dark:bg-surface-700 rounded-lg">
                    <div class="text-display-small font-normal text-secondary-600 dark:text-secondary-400">{{ total_households | default(value="0") }}</div>
                    <div class="text-body-small text-surface-600 dark:text-surface-400">Households</div>
                </div>
                <div class="text-center p-3 bg-surface-50 dark:bg-surface-700 rounded-lg">
                    <div class="text-display-small font-normal text-tertiary-600 dark:text-tertiary-400">{{ total_properties | default(value="0") }}</div>
                    <div class="text-body-small text-surface-600 dark:text-surface-400">Properties</div>
                </div>
                <div class="text-center p-3 bg-surface-50 dark:bg-surface-700 rounded-lg">
                    <div class="text-display-small font-normal text-primary-600 dark:text-primary-400">{{ total_plants | default(value="0") }}</div>
                    <div class="text-body-small text-surface-600 dark:text-surface-400">Plants</div>
                </div>
            </div>
        </div>

        <!-- System Settings -->
        <div class="material-card p-6">
            <div class="flex items-center mb-4">
                <span class="material-icons text-2xl text-tertiary-600 dark:text-tertiary-400 mr-3">settings</span>
                <h2 class="text-title-large font-medium text-surface-900 dark:text-surface-100">System Settings</h2>
            </div>
            <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Configure system-wide settings</p>
            <a href="/admin/settings" class="material-button-outlined">
                Settings
            </a>
        </div>

        <!-- Database Management -->
        <div class="material-card p-6">
            <div class="flex items-center mb-4">
                <span class="material-icons text-2xl text-primary-600 dark:text-primary-400 mr-3">storage</span>
                <h2 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Database Management</h2>
            </div>
            <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Backup, restore, and maintain database</p>
            <a href="/admin/database" class="material-button-outlined">
                Database Tools
            </a>
        </div>

        <!-- HerbaDB Management -->
        <div class="material-card p-6">
            <div class="flex items-center mb-4">
                <span class="material-icons text-2xl text-secondary-600 dark:text-secondary-400 mr-3">local_florist</span>
                <h2 class="text-title-large font-medium text-surface-900 dark:text-surface-100">HerbaDB Management</h2>
            </div>
            <p class="text-body-large text-surface-600 dark:text-surface-300 mb-6">Manage global plant database and botanical information</p>
            <a href="/admin/herba-db" class="material-button-outlined">
                Manage HerbaDB
            </a>
        </div>

        <!-- Notifications -->
        <div class="material-card p-6">
            <div class="flex items-center mb-4">
                <span class="material-icons text-2xl text-tertiary-600 dark:text-tertiary-400 mr-3">notifications</span>
                <h2 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Notification System</h2>
            </div>
            <div class="mb-4">
                <div class="text-center p-3 bg-surface-50 dark:bg-surface-700 rounded-lg">
                    <div class="text-headline-medium font-normal text-tertiary-600 dark:text-tertiary-400">{{ total_notifications | default(value="0") }}</div>
                    <div class="text-body-small text-surface-600 dark:text-surface-400">Total Notifications</div>
                </div>
            </div>
            <a href="/admin/notifications" class="material-button-outlined">
                Manage Notifications
            </a>
        </div>

        <!-- Seeds & HerbaDB -->
        <div class="material-card p-6">
            <div class="flex items-center mb-4">
                <span class="material-icons text-2xl text-primary-600 dark:text-primary-400 mr-3">eco</span>
                <h2 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Seeds & Database</h2>
            </div>
            <div class="grid grid-cols-2 gap-3 mb-4">
                <div class="text-center p-3 bg-surface-50 dark:bg-surface-700 rounded-lg">
                    <div class="text-headline-medium font-normal text-secondary-600 dark:text-secondary-400">{{ total_seeds | default(value="0") }}</div>
                    <div class="text-body-small text-surface-600 dark:text-surface-400">Seeds</div>
                </div>
                <div class="text-center p-3 bg-surface-50 dark:bg-surface-700 rounded-lg">
                    <div class="text-headline-medium font-normal text-tertiary-600 dark:text-tertiary-400">{{ total_herba_plants | default(value="0") }}</div>
                    <div class="text-body-small text-surface-600 dark:text-surface-400">HerbaDB</div>
                </div>
            </div>
            <a href="/admin/herba-db" class="material-button-outlined">
                Manage Database
            </a>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="mt-12 material-card-elevated p-8">
        <div class="flex items-center mb-6">
            <span class="material-icons text-2xl text-primary-600 dark:text-primary-400 mr-3">history</span>
            <h2 class="text-title-large font-medium text-surface-900 dark:text-surface-100">Recent Activity</h2>
        </div>
        <div class="text-body-large text-surface-600 dark:text-surface-300">
            <p>Recent activity logs will be displayed here...</p>
        </div>
    </div>
</div>
{% endblock %}
