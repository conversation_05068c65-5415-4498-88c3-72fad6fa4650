{% extends "base.html" %}
{% block title %}Test Template{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto">
    <h1 class="text-display-small font-normal text-surface-900 dark:text-surface-100 mb-6">Test Page</h1>
    <div class="material-card p-6">
        <p class="text-body-large text-surface-700 dark:text-surface-300 mb-4">This is a test template to check if the basic structure works.</p>
        {% if user_context %}
            <div class="bg-surface-50 dark:bg-surface-700 p-4 rounded-md">
                <p class="text-surface-900 dark:text-surface-100">User context is available: {{ user_context.username }}</p>
                <p class="text-surface-600 dark:text-surface-400 text-sm mt-2">Role: {{ user_context.role | default(value="Not set") }}</p>
                <p class="text-sage-600 dark:text-sage-400 text-sm">Authenticated: {{ user_context.is_authenticated }}</p>
            </div>
        {% else %}
            <div class="bg-yellow-50 dark:bg-yellow-900 p-4 rounded-md">
                <p class="text-yellow-800 dark:text-yellow-200">No user context available</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
