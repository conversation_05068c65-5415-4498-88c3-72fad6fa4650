{% extends "base.html" %}
{% block title %}My Wishlist - Garden Planner{% endblock %}
{% block content %}
<div class="max-w-7xl mx-auto">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
        <div>
            <h1 class="text-headline-large font-normal text-surface-900 dark:text-surface-100">My Wishlist</h1>
            <p class="text-body-large text-surface-600 dark:text-surface-400 mt-2">Plants and seeds you want to add to your garden</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-3">
            <a href="/plants/new" class="material-button-outlined">
                <span class="material-icons text-sm mr-2">add</span>
                Add Plant
            </a>
            <a href="/seeds/new" class="material-button-filled">
                <span class="material-icons text-sm mr-2">add</span>
                Add Seed
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-primary-100 dark:bg-primary-900">
                    <span class="material-icons text-xl text-primary-600 dark:text-primary-300">local_florist</span>
                </div>
                <div class="ml-4">
                    <p class="text-label-medium text-surface-600 dark:text-surface-400">Plant Wishlist</p>
                    <p class="text-display-small font-normal text-surface-900 dark:text-surface-100">{{ plant_wishlist|length }}</p>
                </div>
            </div>
        </div>

        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-secondary-100 dark:bg-secondary-900">
                    <span class="material-icons text-xl text-secondary-600 dark:text-secondary-300">inventory</span>
                </div>
                <div class="ml-4">
                    <p class="text-label-medium text-surface-600 dark:text-surface-400">Seed Wishlist</p>
                    <p class="text-display-small font-normal text-surface-900 dark:text-surface-100">{{ seed_wishlist|length }}</p>
                </div>
            </div>
        </div>

        <div class="material-card p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-tertiary-100 dark:bg-tertiary-900">
                    <span class="material-icons text-xl text-tertiary-600 dark:text-tertiary-300">favorite</span>
                </div>
                <div class="ml-4">
                    <p class="text-label-medium text-surface-600 dark:text-surface-400">Total Items</p>
                    <p class="text-display-small font-normal text-surface-900 dark:text-surface-100">{{ total_wishlist_items | default(value=0) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div class="material-card p-6">
            <h3 class="text-title-large font-normal text-surface-900 dark:text-surface-100 mb-4">Plant Wishlist</h3>
            <p class="text-body-medium text-surface-600 dark:text-surface-300 mb-4">Browse and manage plants you want to grow</p>
            <a href="/wishlist/plants" class="material-button-filled">
                <span class="material-icons text-sm mr-2">local_florist</span>
                View Plant Wishlist
            </a>
        </div>

        <div class="material-card p-6">
            <h3 class="text-title-large font-normal text-surface-900 dark:text-surface-100 mb-4">Seed Wishlist</h3>
            <p class="text-body-medium text-surface-600 dark:text-surface-300 mb-4">Track seeds you want to acquire and plant</p>
            <a href="/wishlist/seeds" class="material-button-filled">
                <span class="material-icons text-sm mr-2">inventory</span>
                View Seed Wishlist
            </a>
        </div>
    </div>

    <!-- Recent Wishlist Items -->
    {% if plant_wishlist or seed_wishlist %}
    <div class="material-card-elevated">
        <div class="px-6 py-4 border-b border-surface-200 dark:border-surface-700">
            <h2 class="text-title-large font-normal text-surface-900 dark:text-surface-100">Recent Wishlist Items</h2>
            <p class="text-body-medium text-surface-600 dark:text-surface-400 mt-1">Your latest wishlist additions</p>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                {% for item in plant_wishlist %}
                {% if loop.index <= 3 %}
                <div class="flex items-center p-4 bg-surface-50 dark:bg-surface-800 rounded-lg">
                    <div class="p-2 rounded-full bg-primary-100 dark:bg-primary-900">
                        <span class="material-icons text-lg text-primary-600 dark:text-primary-300">local_florist</span>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-body-medium font-medium text-surface-900 dark:text-surface-100">{{ item.plant_name | default(value="Unknown Plant") }}</p>
                        <p class="text-body-small text-surface-600 dark:text-surface-400">Plant wishlist item</p>
                    </div>
                    <form method="post" action="/wishlist/remove" class="inline">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <input type="hidden" name="item_type" value="plant">
                        <input type="hidden" name="item_id" value="{{ item.item_id }}">
                        <button type="submit" class="material-button-text text-error-600 hover:text-error-700 dark:text-error-400 dark:hover:text-error-300">
                            Remove
                        </button>
                    </form>
                </div>
                {% endif %}
                {% endfor %}
                
                {% for item in seed_wishlist %}
                {% if loop.index <= 3 %}
                <div class="flex items-center p-4 bg-surface-50 dark:bg-surface-800 rounded-lg">
                    <div class="p-2 rounded-full bg-secondary-100 dark:bg-secondary-900">
                        <span class="material-icons text-lg text-secondary-600 dark:text-secondary-300">inventory</span>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-body-medium font-medium text-surface-900 dark:text-surface-100">{{ item.seed_name | default(value="Unknown Seed") }}</p>
                        <p class="text-body-small text-surface-600 dark:text-surface-400">Seed wishlist item</p>
                    </div>
                    <form method="post" action="/wishlist/remove" class="inline">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <input type="hidden" name="item_type" value="seed">
                        <input type="hidden" name="item_id" value="{{ item.item_id }}">
                        <button type="submit" class="material-button-text text-error-600 hover:text-error-700 dark:text-error-400 dark:hover:text-error-300">
                            Remove
                        </button>
                    </form>
                </div>
                {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
    {% else %}
    <div class="text-center py-16">
        <span class="material-icons text-6xl text-surface-400 dark:text-surface-500 mb-4 block">favorite_border</span>
        <h3 class="text-title-large font-medium text-surface-900 dark:text-surface-100 mb-2">Your wishlist is empty</h3>
        <p class="text-body-large text-surface-600 dark:text-surface-400 mb-8">Start adding plants and seeds you want to grow in your garden.</p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/wishlist/plants" class="material-button-filled">
                <span class="material-icons text-sm mr-2">local_florist</span>
                Browse Plants
            </a>
            <a href="/wishlist/seeds" class="material-button-outlined">
                <span class="material-icons text-sm mr-2">inventory</span>
                Browse Seeds
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
