{% extends "base.html" %}

{% block title %}My Properties{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">My Properties</h1>
            <p class="text-sage-600 dark:text-sage-400 mt-2">Manage and visualize your garden properties</p>
        </div>
        <div class="flex space-x-3">
            <a href="/property/new" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-material shadow-material-2 hover:shadow-material-3 transition-all duration-200">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Property
            </a>
            <a href="/property/wizard" class="bg-secondary-600 hover:bg-secondary-700 text-white px-4 py-2 rounded-material shadow-material-2 hover:shadow-material-3 transition-all duration-200">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                Property Wizard
            </a>
        </div>
    </div>

    {% if properties %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for property in properties %}
                <div class="bg-white dark:bg-sage-800 rounded-material shadow-material-2 hover:shadow-material-3 transition-all duration-200 border border-sage-200 dark:border-sage-700">
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-4">
                            <h3 class="text-xl font-semibold text-sage-900 dark:text-sage-100">{{ property.name }}</h3>
                            <div class="relative">
                                <button onclick="togglePropertyMenu({{ property.id }})" class="text-sage-400 hover:text-sage-600 dark:text-sage-500 dark:hover:text-sage-300 p-1 rounded-full hover:bg-sage-100 dark:hover:bg-sage-700 transition-colors">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                                    </svg>
                                </button>
                                <div id="property-menu-{{ property.id }}" class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-sage-800 rounded-material shadow-material-3 border border-sage-200 dark:border-sage-600 z-10">
                                    <a href="/property/{{ property.id }}/view" class="block px-4 py-2 text-sm text-sage-700 dark:text-sage-300 hover:bg-sage-50 dark:hover:bg-sage-700 transition-colors">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        View
                                    </a>
                                    <a href="/property/{{ property.id }}/edit" class="block px-4 py-2 text-sm text-sage-700 dark:text-sage-300 hover:bg-sage-50 dark:hover:bg-sage-700 transition-colors">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        Edit
                                    </a>
                                    <hr class="border-sage-200 dark:border-sage-600">
                                    <button onclick="deleteProperty({{ property.id }})" class="block w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900 transition-colors">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-sage-600 dark:text-sage-400">Floors</p>
                                        <p class="text-lg font-semibold text-sage-900 dark:text-sage-100">{{ property.floors }}</p>
                                    </div>
                                </div>
                            </div>

                            {% if property.outside_area %}
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                            <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-sage-600 dark:text-sage-400">Outside Area</p>
                                            <p class="text-lg font-semibold text-sage-900 dark:text-sage-100">{{ property.outside_area }} m²</p>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}

                            {% if property.inside_area %}
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                            <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-sage-600 dark:text-sage-400">Inside Area</p>
                                            <p class="text-lg font-semibold text-sage-900 dark:text-sage-100">{{ property.inside_area }} m²</p>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        </div>

                        <div class="mt-6 pt-4 border-t border-sage-200 dark:border-sage-600">
                            <a href="/property/{{ property.id }}/view" class="w-full bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-material shadow-material-1 hover:shadow-material-2 transition-all duration-200 text-center block">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                View Property
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="bg-white dark:bg-sage-800 rounded-material shadow-material-2 border border-sage-200 dark:border-sage-700 text-center py-12">
            <div class="mb-6">
                <svg class="w-20 h-20 mx-auto text-sage-300 dark:text-sage-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
            </div>
            <h3 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-3">No Properties Yet</h3>
            <p class="text-sage-600 dark:text-sage-400 mb-8 max-w-md mx-auto">
                Start by creating your first property to begin planning your garden. Use the wizard for guided setup or create manually.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/property/wizard" class="bg-secondary-600 hover:bg-secondary-700 text-white py-3 px-6 rounded-material shadow-material-2 hover:shadow-material-3 transition-all duration-200 font-medium">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                    Start Property Wizard
                </a>
                <a href="/property/new" class="bg-sage-200 hover:bg-sage-300 dark:bg-sage-700 dark:hover:bg-sage-600 text-sage-800 dark:text-sage-200 py-3 px-6 rounded-material transition-colors font-medium">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Property Manually
                </a>
            </div>
        </div>
    {% endif %}
</div>

<script>
function togglePropertyMenu(propertyId) {
    const menu = document.getElementById(`property-menu-${propertyId}`);
    const allMenus = document.querySelectorAll('[id^="property-menu-"]');

    // Close all other menus
    allMenus.forEach(m => {
        if (m.id !== `property-menu-${propertyId}`) {
            m.classList.add('hidden');
        }
    });

    // Toggle current menu
    menu.classList.toggle('hidden');
}

function deleteProperty(propertyId) {
    window.showConfirm('Are you sure you want to delete this property? This action cannot be undone.', function() {
        fetch(`/property/${propertyId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                window.showSuccess('Property deleted successfully');
                setTimeout(() => location.reload(), 1500);
            } else {
                window.showError('Failed to delete property');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            window.showError('Failed to delete property');
        });
    });
}

// Close menus when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('[onclick^="togglePropertyMenu"]') && !event.target.closest('[id^="property-menu-"]')) {
        const allMenus = document.querySelectorAll('[id^="property-menu-"]');
        allMenus.forEach(menu => menu.classList.add('hidden'));
    }
});
</script>
{% endblock %}
