#!/usr/bin/env python3
"""
Comprehensive User Flow Test for Garden Planner Web Application
Tests complete user journeys including registration, login, navigation, and core features
"""

import subprocess
import time
import urllib.request
import urllib.parse
import urllib.error
import sys
import os
import signal
import http.cookiejar

def start_server():
    """Start the Rust server in the background"""
    print("Starting Garden Planner server...")
    try:
        process = subprocess.Popen(
            ["cargo", "run"],
            cwd="/home/<USER>/dev/garden_planner_web",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            preexec_fn=os.setsid
        )
        
        print("Waiting for server to start...")
        time.sleep(10)
        
        return process
    except Exception as e:
        print(f"Failed to start server: {e}")
        return None

def test_user_flow():
    """Test complete user flow"""
    base_url = "http://localhost:8080"
    
    # Create cookie jar for session management
    cookie_jar = http.cookiejar.CookieJar()
    opener = urllib.request.build_opener(urllib.request.HTTPCookieProcessor(cookie_jar))
    
    tests_passed = 0
    total_tests = 0
    
    print(f"\nTesting complete user flow...\n")
    
    # Test 1: Homepage loads
    total_tests += 1
    try:
        response = opener.open(f"{base_url}/")
        if response.getcode() == 200:
            content = response.read().decode('utf-8')
            if '<html' in content.lower() and 'garden planner' in content.lower():
                print("✓ Homepage loads correctly")
                tests_passed += 1
            else:
                print("✗ Homepage content invalid")
        else:
            print(f"✗ Homepage returned status {response.getcode()}")
    except Exception as e:
        print(f"✗ Homepage test failed: {e}")
    
    # Test 2: Registration page loads
    total_tests += 1
    try:
        response = opener.open(f"{base_url}/auth/register")
        if response.getcode() == 200:
            content = response.read().decode('utf-8')
            if 'register' in content.lower() and 'form' in content.lower():
                print("✓ Registration page loads correctly")
                tests_passed += 1
            else:
                print("✗ Registration page content invalid")
        else:
            print(f"✗ Registration page returned status {response.getcode()}")
    except Exception as e:
        print(f"✗ Registration page test failed: {e}")
    
    # Test 3: Login page loads
    total_tests += 1
    try:
        response = opener.open(f"{base_url}/auth/login")
        if response.getcode() == 200:
            content = response.read().decode('utf-8')
            if 'login' in content.lower() and 'form' in content.lower():
                print("✓ Login page loads correctly")
                tests_passed += 1
            else:
                print("✗ Login page content invalid")
        else:
            print(f"✗ Login page returned status {response.getcode()}")
    except Exception as e:
        print(f"✗ Login page test failed: {e}")
    
    # Test 4: Dashboard redirects to login when not authenticated
    total_tests += 1
    try:
        response = opener.open(f"{base_url}/dashboard")
        content = response.read().decode('utf-8')
        if 'login' in content.lower() or response.getcode() in [200, 302]:
            print("✓ Dashboard properly handles unauthenticated access")
            tests_passed += 1
        else:
            print(f"✗ Dashboard authentication check failed")
    except Exception as e:
        print(f"✗ Dashboard test failed: {e}")
    
    # Test 5: Plants page handles unauthenticated access
    total_tests += 1
    try:
        response = opener.open(f"{base_url}/plants/list")
        content = response.read().decode('utf-8')
        if 'login' in content.lower() or response.getcode() in [200, 302]:
            print("✓ Plants page properly handles unauthenticated access")
            tests_passed += 1
        else:
            print(f"✗ Plants page authentication check failed")
    except Exception as e:
        print(f"✗ Plants page test failed: {e}")
    
    # Test 6: Seeds page handles unauthenticated access
    total_tests += 1
    try:
        response = opener.open(f"{base_url}/seeds/list")
        content = response.read().decode('utf-8')
        if 'login' in content.lower() or response.getcode() in [200, 302]:
            print("✓ Seeds page properly handles unauthenticated access")
            tests_passed += 1
        else:
            print(f"✗ Seeds page authentication check failed")
    except Exception as e:
        print(f"✗ Seeds page test failed: {e}")
    
    # Test 7: Admin page handles unauthenticated access
    total_tests += 1
    try:
        response = opener.open(f"{base_url}/admin")
        content = response.read().decode('utf-8')
        if 'login' in content.lower() or 'admin' in content.lower() or response.getcode() in [200, 302]:
            print("✓ Admin page properly handles unauthenticated access")
            tests_passed += 1
        else:
            print(f"✗ Admin page authentication check failed")
    except Exception as e:
        print(f"✗ Admin page test failed: {e}")
    
    # Test 8: CSS and static assets load
    total_tests += 1
    try:
        response = opener.open(f"{base_url}/static/css/output.css")
        if response.getcode() == 200:
            content = response.read().decode('utf-8')
            if 'material' in content.lower() or 'primary' in content.lower():
                print("✓ CSS loads correctly with Material 3 styling")
                tests_passed += 1
            else:
                print("✗ CSS content invalid")
        else:
            print(f"✗ CSS returned status {response.getcode()}")
    except Exception as e:
        print(f"✗ CSS test failed: {e}")
    
    # Test 9: Navigation consistency
    total_tests += 1
    try:
        pages_to_check = ["/", "/auth/login", "/auth/register"]
        nav_consistent = True
        
        for page in pages_to_check:
            response = opener.open(f"{base_url}{page}")
            content = response.read().decode('utf-8')
            if '<nav' not in content or 'garden planner' not in content.lower():
                nav_consistent = False
                break
        
        if nav_consistent:
            print("✓ Navigation is consistent across pages")
            tests_passed += 1
        else:
            print("✗ Navigation inconsistency detected")
    except Exception as e:
        print(f"✗ Navigation test failed: {e}")
    
    # Test 10: Material 3 design elements present
    total_tests += 1
    try:
        response = opener.open(f"{base_url}/")
        content = response.read().decode('utf-8')
        material3_elements = ['material-', 'primary-', 'surface-', 'text-display', 'text-body']
        has_material3 = any(element in content for element in material3_elements)
        
        if has_material3:
            print("✓ Material 3 design elements present")
            tests_passed += 1
        else:
            print("✗ Material 3 design elements missing")
    except Exception as e:
        print(f"✗ Material 3 design test failed: {e}")
    
    print(f"\nUser Flow Test Results: {tests_passed}/{total_tests} tests passed")
    return tests_passed == total_tests

def main():
    """Main test function"""
    print("Garden Planner Web Application - Comprehensive User Flow Test")
    print("=" * 65)
    
    # Start the server
    server_process = start_server()
    if not server_process:
        print("Failed to start server. Exiting.")
        return False
    
    try:
        # Run user flow tests
        success = test_user_flow()
        
        if success:
            print("\n✓ All user flow tests passed!")
            print("The Garden Planner application user flows are working correctly.")
        else:
            print("\n✗ Some user flow tests failed.")
            print("Please check the application for issues.")
        
        return success
        
    finally:
        # Clean up: stop the server
        print("\nStopping server...")
        try:
            os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
            server_process.wait(timeout=5)
        except:
            try:
                os.killpg(os.getpgid(server_process.pid), signal.SIGKILL)
            except:
                pass
        print("Server stopped.")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
